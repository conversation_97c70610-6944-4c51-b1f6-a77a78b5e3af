@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   🚀 بناء النسخة المحمولة من Inzo IB
echo ========================================
echo.

:: التأكد من وجود .NET 6.0
echo 🔍 فحص .NET 6.0...
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: .NET 6.0 غير مثبت على النظام
    echo يرجى تثبيت .NET 6.0 SDK من: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

:: الانتقال إلى مجلد المشروع
cd /d "%~dp0.."

:: تنظيف البناء السابق
echo 🧹 تنظيف البناء السابق...
dotnet clean --configuration Release >nul 2>&1

:: بناء النسخة المحمولة
echo 🔨 بناء النسخة المحمولة...
dotnet publish -c Release -r win-x64 --self-contained true -o "Portable_Build\InzoIB_Portable" --verbosity quiet

if errorlevel 1 (
    echo ❌ فشل في بناء النسخة المحمولة
    pause
    exit /b 1
)

:: إنشاء مجلد البيانات
echo 📁 إنشاء مجلد البيانات...
if not exist "Portable_Build\InzoIB_Portable\Data" mkdir "Portable_Build\InzoIB_Portable\Data"
if not exist "Portable_Build\InzoIB_Portable\Data\Attachments" mkdir "Portable_Build\InzoIB_Portable\Data\Attachments"

:: نسخ ملف البيانات إذا كان موجوداً
if exist "content_data.json" (
    echo 📋 نسخ ملف البيانات...
    copy "content_data.json" "Portable_Build\InzoIB_Portable\Data\" >nul
)

:: نسخ الملفات المرفقة إذا كانت موجودة
if exist "bin\Release\net6.0-windows\Data\Attachments" (
    echo 📎 نسخ الملفات المرفقة...
    xcopy "bin\Release\net6.0-windows\Data\Attachments\*" "Portable_Build\InzoIB_Portable\Data\Attachments\" /E /I /Q >nul 2>&1
)

:: إنشاء ملف التعليمات
echo 📝 إنشاء ملف التعليمات...
call :create_instructions

:: إنشاء ملف تشغيل سريع
echo 🚀 إنشاء ملف التشغيل السريع...
call :create_launcher

echo.
echo ✅ تم بناء النسخة المحمولة بنجاح!
echo 📁 المجلد: Portable_Build\InzoIB_Portable
echo.
echo 🎯 يمكنك الآن نسخ مجلد "InzoIB_Portable" إلى أي جهاز آخر
echo    والبرنامج سيعمل بدون الحاجة لتثبيت أي شيء إضافي!
echo.
pause
exit /b 0

:create_instructions
(
echo 🎯 Inzo IB v7.4 - النسخة المحمولة
echo =====================================
echo.
echo 📦 هذه النسخة المحمولة تحتوي على:
echo • جميع ملفات .NET Runtime المطلوبة
echo • جميع المكتبات والتبعيات
echo • مجلد البيانات والملفات المرفقة
echo • لا تحتاج لتثبيت أي برامج إضافية
echo.
echo 🚀 كيفية الاستخدام:
echo 1. انقر مرتين على "تشغيل البرنامج.bat"
echo    أو انقر مرتين على "InzoIB_v7.4_Simple.exe"
echo.
echo 2. للنقل إلى جهاز آخر:
echo    • انسخ مجلد "InzoIB_Portable" كاملاً
echo    • الصقه في أي مكان في الجهاز الجديد
echo    • شغل البرنامج مباشرة
echo.
echo 📁 هيكل الملفات:
echo ├── InzoIB_v7.4_Simple.exe     # البرنامج الرئيسي
echo ├── تشغيل البرنامج.bat          # ملف التشغيل السريع
echo ├── Data/                      # مجلد البيانات
echo │   ├── content_data.json      # ملف البيانات
echo │   └── Attachments/           # الملفات المرفقة
echo └── ملفات .NET Runtime...      # ملفات النظام المطلوبة
echo.
echo ✅ مميزات النسخة المحمولة:
echo • تعمل على أي جهاز Windows 10/11
echo • لا تحتاج تثبيت .NET Framework
echo • لا تحتاج صلاحيات مدير النظام
echo • جميع البيانات محفوظة محلياً
echo • سرعة في التشغيل
echo.
echo 📞 للدعم الفني: تواصل مع فريق التطوير
echo 📅 تاريخ البناء: %date% %time%
echo 🏷️ إصدار البرنامج: Inzo IB v7.4 - Portable
) > "Portable_Build\InzoIB_Portable\📖 تعليمات الاستخدام.txt"
goto :eof

:create_launcher
(
echo @echo off
echo chcp 65001 ^>nul
echo title Inzo IB v7.4 - Simple Version
echo.
echo echo 🚀 تشغيل Inzo IB v7.4...
echo echo.
echo start "" "InzoIB_v7.4_Simple.exe"
echo exit
) > "Portable_Build\InzoIB_Portable\تشغيل البرنامج.bat"
goto :eof
