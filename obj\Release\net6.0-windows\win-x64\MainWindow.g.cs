﻿#pragma checksum "..\..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0A9B25F091D64CE350DCBD3135053D4355D512CD"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InzoIB_Simple {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 30 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnTawdeeh;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRodod;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnOmala;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnFaezeen;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnMosabakat;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnWokala;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSahbWaEeeda;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnInzoIB;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnNasekh;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnTaaweedWaTadkeek;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnManshoorat;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSowar;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnMeeting;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ContentScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ContentPanel;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TaskSearchBox;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TaskCopyButton;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TaskEditButton;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TaskDeleteButton;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TaskFilesButton;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TaskMoveUpButton;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TaskMoveDownButton;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TaskDividerButton;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TaskAddButton;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AISettingsButton;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimeText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InzoIB_v7.4_Simple;V7.4.0.0;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnTawdeeh = ((System.Windows.Controls.Button)(target));
            
            #line 33 "..\..\..\..\MainWindow.xaml"
            this.BtnTawdeeh.Click += new System.Windows.RoutedEventHandler(this.NavigateToSection);
            
            #line default
            #line hidden
            return;
            case 2:
            this.BtnRodod = ((System.Windows.Controls.Button)(target));
            
            #line 37 "..\..\..\..\MainWindow.xaml"
            this.BtnRodod.Click += new System.Windows.RoutedEventHandler(this.NavigateToSection);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnOmala = ((System.Windows.Controls.Button)(target));
            
            #line 41 "..\..\..\..\MainWindow.xaml"
            this.BtnOmala.Click += new System.Windows.RoutedEventHandler(this.NavigateToSection);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnFaezeen = ((System.Windows.Controls.Button)(target));
            
            #line 45 "..\..\..\..\MainWindow.xaml"
            this.BtnFaezeen.Click += new System.Windows.RoutedEventHandler(this.NavigateToSection);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnMosabakat = ((System.Windows.Controls.Button)(target));
            
            #line 49 "..\..\..\..\MainWindow.xaml"
            this.BtnMosabakat.Click += new System.Windows.RoutedEventHandler(this.NavigateToSection);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnWokala = ((System.Windows.Controls.Button)(target));
            
            #line 53 "..\..\..\..\MainWindow.xaml"
            this.BtnWokala.Click += new System.Windows.RoutedEventHandler(this.NavigateToSection);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnSahbWaEeeda = ((System.Windows.Controls.Button)(target));
            
            #line 57 "..\..\..\..\MainWindow.xaml"
            this.BtnSahbWaEeeda.Click += new System.Windows.RoutedEventHandler(this.NavigateToSection);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BtnInzoIB = ((System.Windows.Controls.Button)(target));
            
            #line 61 "..\..\..\..\MainWindow.xaml"
            this.BtnInzoIB.Click += new System.Windows.RoutedEventHandler(this.NavigateToSection);
            
            #line default
            #line hidden
            return;
            case 9:
            this.BtnNasekh = ((System.Windows.Controls.Button)(target));
            
            #line 65 "..\..\..\..\MainWindow.xaml"
            this.BtnNasekh.Click += new System.Windows.RoutedEventHandler(this.NavigateToSection);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BtnTaaweedWaTadkeek = ((System.Windows.Controls.Button)(target));
            
            #line 69 "..\..\..\..\MainWindow.xaml"
            this.BtnTaaweedWaTadkeek.Click += new System.Windows.RoutedEventHandler(this.NavigateToSection);
            
            #line default
            #line hidden
            return;
            case 11:
            this.BtnManshoorat = ((System.Windows.Controls.Button)(target));
            
            #line 73 "..\..\..\..\MainWindow.xaml"
            this.BtnManshoorat.Click += new System.Windows.RoutedEventHandler(this.NavigateToSection);
            
            #line default
            #line hidden
            return;
            case 12:
            this.BtnSowar = ((System.Windows.Controls.Button)(target));
            
            #line 77 "..\..\..\..\MainWindow.xaml"
            this.BtnSowar.Click += new System.Windows.RoutedEventHandler(this.NavigateToSection);
            
            #line default
            #line hidden
            return;
            case 13:
            this.BtnMeeting = ((System.Windows.Controls.Button)(target));
            
            #line 81 "..\..\..\..\MainWindow.xaml"
            this.BtnMeeting.Click += new System.Windows.RoutedEventHandler(this.NavigateToSection);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ContentScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 15:
            this.ContentPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 16:
            this.TaskSearchBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 134 "..\..\..\..\MainWindow.xaml"
            this.TaskSearchBox.GotFocus += new System.Windows.RoutedEventHandler(this.TaskSearchBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 135 "..\..\..\..\MainWindow.xaml"
            this.TaskSearchBox.LostFocus += new System.Windows.RoutedEventHandler(this.TaskSearchBox_LostFocus);
            
            #line default
            #line hidden
            
            #line 136 "..\..\..\..\MainWindow.xaml"
            this.TaskSearchBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TaskSearchBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.TaskCopyButton = ((System.Windows.Controls.Button)(target));
            
            #line 144 "..\..\..\..\MainWindow.xaml"
            this.TaskCopyButton.Click += new System.Windows.RoutedEventHandler(this.TaskCopyButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.TaskEditButton = ((System.Windows.Controls.Button)(target));
            
            #line 157 "..\..\..\..\MainWindow.xaml"
            this.TaskEditButton.Click += new System.Windows.RoutedEventHandler(this.TaskEditButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.TaskDeleteButton = ((System.Windows.Controls.Button)(target));
            
            #line 162 "..\..\..\..\MainWindow.xaml"
            this.TaskDeleteButton.Click += new System.Windows.RoutedEventHandler(this.TaskDeleteButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.TaskFilesButton = ((System.Windows.Controls.Button)(target));
            
            #line 167 "..\..\..\..\MainWindow.xaml"
            this.TaskFilesButton.Click += new System.Windows.RoutedEventHandler(this.TaskFilesButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.TaskMoveUpButton = ((System.Windows.Controls.Button)(target));
            
            #line 174 "..\..\..\..\MainWindow.xaml"
            this.TaskMoveUpButton.Click += new System.Windows.RoutedEventHandler(this.TaskMoveUpButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.TaskMoveDownButton = ((System.Windows.Controls.Button)(target));
            
            #line 179 "..\..\..\..\MainWindow.xaml"
            this.TaskMoveDownButton.Click += new System.Windows.RoutedEventHandler(this.TaskMoveDownButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.TaskDividerButton = ((System.Windows.Controls.Button)(target));
            
            #line 186 "..\..\..\..\MainWindow.xaml"
            this.TaskDividerButton.Click += new System.Windows.RoutedEventHandler(this.TaskDividerButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.TaskAddButton = ((System.Windows.Controls.Button)(target));
            
            #line 191 "..\..\..\..\MainWindow.xaml"
            this.TaskAddButton.Click += new System.Windows.RoutedEventHandler(this.TaskAddButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.AISettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 198 "..\..\..\..\MainWindow.xaml"
            this.AISettingsButton.Click += new System.Windows.RoutedEventHandler(this.AISettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.TimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

