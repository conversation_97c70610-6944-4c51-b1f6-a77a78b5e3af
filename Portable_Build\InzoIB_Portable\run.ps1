# PowerShell Launcher for Inzo IB v7.4 Portable
# تشغيل Inzo IB v7.4 النسخة المحمولة

# إعداد الترميز
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# إعداد النافذة
$Host.UI.RawUI.WindowTitle = "Inzo IB v7.4 - Portable Launcher"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   🚀 Inzo IB v7.4 - Portable Version" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# عرض معلومات المجلد الحالي
$currentPath = Get-Location
Write-Host "📂 المجلد الحالي: $currentPath" -ForegroundColor Blue
Write-Host "🎯 تحميل التطبيق..." -ForegroundColor Blue
Write-Host ""

# التحقق من وجود الملف التنفيذي
$exePath = "InzoIB_v7.4_Simple.exe"
if (!(Test-Path $exePath)) {
    Write-Host "❌ خطأ: لم يتم العثور على $exePath!" -ForegroundColor Red
    Write-Host "يرجى التأكد من وجود جميع الملفات في المكان الصحيح." -ForegroundColor Yellow
    Write-Host ""
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# التحقق من مجلد البيانات
if (!(Test-Path "Data")) {
    Write-Host "⚠️ تحذير: مجلد البيانات غير موجود. سيتم إنشاؤه تلقائياً." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path "Data" -Force | Out-Null
    New-Item -ItemType Directory -Path "Data\Attachments" -Force | Out-Null
}

# تشغيل التطبيق
try {
    Write-Host "✅ بدء تشغيل Inzo IB v7.4..." -ForegroundColor Green
    
    # تشغيل التطبيق في نافذة منفصلة
    Start-Process -FilePath $exePath -WorkingDirectory (Get-Location)
    
    # انتظار قصير للتأكد من بدء التشغيل
    Start-Sleep -Seconds 2
    
    Write-Host "🎉 تم تشغيل التطبيق بنجاح!" -ForegroundColor Green
    Write-Host "يمكنك إغلاق هذه النافذة الآن." -ForegroundColor Gray
    Write-Host ""
    
    # انتظار قبل الإغلاق التلقائي
    Start-Sleep -Seconds 3
    
} catch {
    Write-Host "❌ خطأ في تشغيل التطبيق: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# معلومات إضافية
Write-Host "💡 نصائح:" -ForegroundColor Cyan
Write-Host "• يمكنك إنشاء اختصار لهذا الملف على سطح المكتب" -ForegroundColor Gray
Write-Host "• جميع بياناتك محفوظة في مجلد 'Data'" -ForegroundColor Gray
Write-Host "• للنقل إلى جهاز آخر، انسخ المجلد كاملاً" -ForegroundColor Gray
Write-Host ""
