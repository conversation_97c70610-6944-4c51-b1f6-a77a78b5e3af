@echo off
chcp 65001 >nul 2>&1
title Backup Data - Inzo IB v7.4

echo.
echo ========================================
echo   💾 نسخ احتياطي للبيانات - Inzo IB v7.4
echo ========================================
echo.

:: التحقق من وجود مجلد البيانات
if not exist "Data" (
    echo ❌ خطأ: مجلد البيانات غير موجود!
    echo تأكد من تشغيل هذا الملف من مجلد البرنامج.
    echo.
    pause
    exit /b 1
)

:: إنشاء اسم النسخة الاحتياطية مع التاريخ والوقت
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "datestamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%"

set "backupName=InzoIB_Backup_%datestamp%"
set "backupPath=Backups\%backupName%"

echo 📅 التاريخ والوقت: %YYYY%-%MM%-%DD% %HH%:%Min%:%Sec%
echo 📁 اسم النسخة الاحتياطية: %backupName%
echo 📂 مسار النسخة الاحتياطية: %backupPath%
echo.

:: إنشاء مجلد النسخ الاحتياطية
if not exist "Backups" (
    echo 📁 إنشاء مجلد النسخ الاحتياطية...
    mkdir "Backups"
)

:: إنشاء مجلد النسخة الاحتياطية الحالية
echo 📁 إنشاء مجلد النسخة الاحتياطية...
mkdir "%backupPath%"

:: نسخ مجلد البيانات
echo 📋 نسخ ملف البيانات الرئيسي...
if exist "Data\content_data.json" (
    copy "Data\content_data.json" "%backupPath%\" >nul
    echo ✅ تم نسخ content_data.json
) else (
    echo ⚠️  ملف content_data.json غير موجود
)

:: نسخ الملفات المرفقة
echo 📎 نسخ الملفات المرفقة...
if exist "Data\Attachments" (
    xcopy "Data\Attachments" "%backupPath%\Attachments\" /E /I /Q >nul 2>&1
    if %errorlevel%==0 (
        echo ✅ تم نسخ الملفات المرفقة
    ) else (
        echo ⚠️  لا توجد ملفات مرفقة للنسخ
    )
) else (
    echo ⚠️  مجلد الملفات المرفقة غير موجود
)

:: إنشاء ملف معلومات النسخة الاحتياطية
echo 📝 إنشاء ملف معلومات النسخة الاحتياطية...
(
echo 💾 معلومات النسخة الاحتياطية - Inzo IB v7.4
echo =============================================
echo.
echo 📅 تاريخ الإنشاء: %YYYY%-%MM%-%DD%
echo 🕐 وقت الإنشاء: %HH%:%Min%:%Sec%
echo 📁 اسم النسخة: %backupName%
echo 🖥️  اسم الجهاز: %COMPUTERNAME%
echo 👤 اسم المستخدم: %USERNAME%
echo.
echo 📋 محتويات النسخة الاحتياطية:
echo • ملف البيانات الرئيسي ^(content_data.json^)
echo • جميع الملفات المرفقة ^(مجلد Attachments^)
echo.
echo 🔄 لاستعادة النسخة الاحتياطية:
echo 1. انسخ محتويات هذا المجلد
echo 2. الصقها في مجلد "Data" في البرنامج
echo 3. استبدل الملفات الموجودة عند السؤال
echo.
echo ⚠️  ملاحظة: احتفظ بهذه النسخة في مكان آمن
echo.
echo تم إنشاء هذا الملف تلقائياً بواسطة أداة النسخ الاحتياطي
echo Inzo IB v7.4 - Backup Tool
) > "%backupPath%\📖 معلومات النسخة الاحتياطية.txt"

:: حساب حجم النسخة الاحتياطية
echo 📊 حساب حجم النسخة الاحتياطية...
for /f "tokens=3" %%a in ('dir "%backupPath%" /s /-c ^| find "bytes"') do set "backupSize=%%a"

echo.
echo ========================================
echo           ✅ تمت العملية بنجاح!
echo ========================================
echo.
echo 📁 مجلد النسخة الاحتياطية: %backupPath%
echo 📊 حجم النسخة الاحتياطية: %backupSize% bytes
echo.
echo 💡 نصائح:
echo • احتفظ بالنسخة الاحتياطية في مكان آمن
echo • اعمل نسخة احتياطية دورياً ^(أسبوعياً مثلاً^)
echo • يمكنك نسخ مجلد النسخة الاحتياطية إلى قرص خارجي
echo.

:: عرض محتويات مجلد النسخ الاحتياطية
echo 📂 النسخ الاحتياطية المتاحة:
if exist "Backups" (
    dir "Backups" /B /AD
) else (
    echo لا توجد نسخ احتياطية أخرى
)
echo.

set /p "openFolder=هل تريد فتح مجلد النسخة الاحتياطية؟ (y/n): "
if /i "%openFolder%"=="y" (
    start "" "%backupPath%"
)

echo.
pause
