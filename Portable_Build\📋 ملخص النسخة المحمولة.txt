🎯 Inzo IB v7.4 - ملخص النسخة المحمولة
=========================================

✅ تم إنشاء النسخة المحمولة بنجاح!

📁 المجلد المحمول: Portable_Build\InzoIB_Portable
📅 تاريخ الإنشاء: 2025-01-07
🏷️ إصدار البرنامج: v7.4 Portable
🖥️ نوع البناء: Self-Contained Deployment

🎯 المميزات الجديدة:
===================

✅ محمول بالكامل:
• يتضمن جميع ملفات .NET 6.0 Runtime
• لا يحتاج تثبيت أي برامج إضافية
• يعمل على أي جهاز Windows 10/11 (64-bit)

✅ أدوات مساعدة:
• run.bat - تشغيل سريع مع رسائل توضيحية
• run.ps1 - تشغيل PowerShell مع فحوصات إضافية
• system_check.bat - فحص توافق النظام
• backup_data.bat - إنشاء نسخة احتياطية
• restore_data.bat - استعادة نسخة احتياطية

✅ إدارة البيانات:
• مجلد Data منفصل لجميع البيانات
• نظام نسخ احتياطي تلقائي مع التاريخ والوقت
• حفظ الملفات المرفقة محلياً
• عدم الاعتماد على مسارات مطلقة

✅ سهولة النقل:
• نسخ ولصق المجلد كاملاً
• لا يحتاج إعدادات إضافية
• يحتفظ بجميع البيانات والإعدادات

📂 هيكل الملفات:
================

InzoIB_Portable/
├── 🚀 ملفات التشغيل
│   ├── InzoIB_v7.4_Simple.exe
│   ├── run.bat
│   └── run.ps1
├── 🔧 أدوات النظام
│   ├── system_check.bat
│   ├── backup_data.bat
│   └── restore_data.bat
├── 📖 التوثيق
│   ├── README.txt
│   └── 📖 دليل الاستخدام - النسخة المحمولة.txt
├── 📂 البيانات
│   └── Data/
│       ├── content_data.json
│       └── Attachments/
├── 🔄 النسخ الاحتياطية
│   └── Backups/ (يتم إنشاؤه عند الحاجة)
└── 🏗️ ملفات .NET Runtime
    ├── coreclr.dll
    ├── hostfxr.dll
    ├── hostpolicy.dll
    ├── System.*.dll
    └── مجلدات اللغات (cs, de, es, fr, it, ja, ko, pl, pt-BR, ru, tr, zh-Hans, zh-Hant)

🚀 كيفية الاستخدام:
==================

1️⃣ التشغيل لأول مرة:
• شغل "system_check.bat" للتأكد من التوافق
• شغل "run.bat" لتشغيل البرنامج

2️⃣ الاستخدام اليومي:
• انقر مرتين على "run.bat"
• أو انقر مرتين على "InzoIB_v7.4_Simple.exe"

3️⃣ النسخ الاحتياطية:
• شغل "backup_data.bat" لإنشاء نسخة احتياطية
• شغل "restore_data.bat" لاستعادة نسخة احتياطية

4️⃣ النقل إلى جهاز آخر:
• انسخ مجلد "InzoIB_Portable" كاملاً
• الصقه في الجهاز الجديد
• شغل البرنامج مباشرة

🔧 المتطلبات:
=============
• Windows 10 version 1809 أو أحدث
• Windows 11 (مُوصى به)
• معمارية x64
• 4 GB ذاكرة (8 GB مُوصى به)
• 200 MB مساحة قرص فارغة

⚠️ ملاحظات مهمة:
================
• حجم المجلد حوالي 150-200 MB (طبيعي للنسخة المحمولة)
• لا تحذف أو تنقل ملفات من مجلد البرنامج
• احتفظ بنسخة احتياطية من مجلد "Data"
• البرنامج آمن ولا يؤثر على النظام
• لا يحتاج صلاحيات مدير النظام

🎊 مقارنة مع النسخة العادية:
=============================

النسخة العادية:
❌ تحتاج تثبيت .NET Framework
❌ تحتاج صلاحيات مدير
❌ تعتمد على النظام
✅ حجم أصغر

النسخة المحمولة:
✅ لا تحتاج تثبيت أي شيء
✅ لا تحتاج صلاحيات مدير
✅ مستقلة تماماً
✅ سهولة النقل
❌ حجم أكبر

🏆 التوصية:
============
استخدم النسخة المحمولة إذا كنت:
• تريد نقل البرنامج بين أجهزة مختلفة
• لا تملك صلاحيات مدير النظام
• تريد برنامج مستقل لا يؤثر على النظام
• تعمل في بيئة محدودة الصلاحيات

📞 الدعم الفني:
===============
للمساعدة أو الإبلاغ عن مشاكل:
• تواصل مع فريق التطوير
• اذكر نوع النسخة: "Portable v7.4"
• أرفق ملف "system_check.bat" إذا طُلب منك

🎉 استمتع بالنسخة المحمولة الجديدة!

═══════════════════════════════════════════════════════════════
تم إنشاء هذا الملف تلقائياً بواسطة أداة البناء المحمول
Inzo IB v7.4 - Portable Build Tool
تاريخ الإنشاء: 2025-01-07
═══════════════════════════════════════════════════════════════
