@echo off
chcp 65001 >nul 2>&1
title Inzo IB v7.4 - Portable Version

echo.
echo ========================================
echo    🚀 Inzo IB v7.4 - Portable Version
echo ========================================
echo.
echo 📂 Starting from: %~dp0
echo 🎯 Loading application...
echo.

:: Check if the executable exists
if not exist "InzoIB_v7.4_Simple.exe" (
    echo ❌ Error: InzoIB_v7.4_Simple.exe not found!
    echo Please make sure all files are in the correct location.
    echo.
    pause
    exit /b 1
)

:: Start the application
echo ✅ Starting Inzo IB v7.4...
start "" "InzoIB_v7.4_Simple.exe"

:: Wait a moment to see if it starts successfully
timeout /t 2 /nobreak >nul

echo 🎉 Application started successfully!
echo You can close this window now.
echo.
timeout /t 3 /nobreak >nul
