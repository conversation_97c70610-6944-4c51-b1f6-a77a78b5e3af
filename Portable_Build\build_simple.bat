@echo off
echo Building Portable Version...

cd /d "%~dp0.."

echo Cleaning previous build...
dotnet clean --configuration Release

echo Building portable version...
dotnet publish -c Release -r win-x64 --self-contained true -o "Portable_Build\InzoIB_Portable"

if errorlevel 1 (
    echo Build failed!
    pause
    exit /b 1
)

echo Creating data folders...
if not exist "Portable_Build\InzoIB_Portable\Data" mkdir "Portable_Build\InzoIB_Portable\Data"
if not exist "Portable_Build\InzoIB_Portable\Data\Attachments" mkdir "Portable_Build\InzoIB_Portable\Data\Attachments"

echo Copying data files...
if exist "content_data.json" copy "content_data.json" "Portable_Build\InzoIB_Portable\Data\"

if exist "bin\Release\net6.0-windows\Data\Attachments" (
    xcopy "bin\Release\net6.0-windows\Data\Attachments\*" "Portable_Build\InzoIB_Portable\Data\Attachments\" /E /I /Q
)

echo Creating launcher...
echo @echo off > "Portable_Build\InzoIB_Portable\run.bat"
echo title Inzo IB v7.4 >> "Portable_Build\InzoIB_Portable\run.bat"
echo start "" "InzoIB_v7.4_Simple.exe" >> "Portable_Build\InzoIB_Portable\run.bat"

echo.
echo Build completed successfully!
echo Folder: Portable_Build\InzoIB_Portable
echo.
pause
