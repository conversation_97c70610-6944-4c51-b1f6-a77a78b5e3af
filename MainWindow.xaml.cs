using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Linq;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Runtime.InteropServices;
using System.Windows.Interop;
using System.Windows.Input;
using System.Net.Http;
using System.Net.NetworkInformation;
using System.Security.Cryptography;
using OpenAI;
using OpenAI.Chat;

namespace InzoIB_Simple
{
    public partial class MainWindow : Window
    {
        #region Windows API للتحكم في شريط العنوان

        [DllImport("dwmapi.dll")]
        private static extern int DwmSetWindowAttribute(IntPtr hwnd, int attr, ref int attrValue, int attrSize);

        [DllImport("dwmapi.dll")]
        private static extern int DwmIsCompositionEnabled(out bool enabled);

        [DllImport("user32.dll")]
        private static extern IntPtr GetWindowLong(IntPtr hWnd, int nIndex);

        [DllImport("user32.dll")]
        private static extern int SetWindowLong(IntPtr hWnd, int nIndex, IntPtr dwNewLong);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetModuleHandle(string lpModuleName);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        private const int GWL_EXSTYLE = -20;
        private const int WS_EX_DLGMODALFRAME = 0x0001;
        private const int SWP_NOSIZE = 0x0001;
        private const int SWP_NOMOVE = 0x0002;
        private const int SWP_NOZORDER = 0x0004;
        private const int SWP_FRAMECHANGED = 0x0020;
        private const int DWMWA_CAPTION_COLOR = 35;
        private const int DWMWA_USE_IMMERSIVE_DARK_MODE = 20;

        #endregion
        private DispatcherTimer timer;
        private string currentSection = "توضيح";
        private List<ContentItem> contentItems;
        private ContentItem selectedItem;

        // مسار حفظ البيانات
        private readonly string dataFolderPath;
        private readonly string dataFilePath;

        // Network Configuration
        private static readonly HttpClient httpClient = new HttpClient();
        private bool isInternetConnected = false;
        private DispatcherTimer networkCheckTimer;

        // OpenAI Configuration
        private ChatClient openAIClient;
        private string openAIApiKey = "YOUR_OPENAI_API_KEY_HERE"; // سيتم تحميله من الملف
        private readonly string apiKeyFilePath;

        public MainWindow()
        {
            try
            {
                // تهيئة مسارات الحفظ (نسبي للنقل بين الأجهزة)
                // استخدام مسار نسبي بجوار ملف البرنامج
                var executablePath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                var executableDirectory = Path.GetDirectoryName(executablePath);
                dataFolderPath = Path.Combine(executableDirectory, "Data");
                dataFilePath = Path.Combine(dataFolderPath, "content_data.json");
                apiKeyFilePath = Path.Combine(dataFolderPath, "api_key.dat");

                System.Diagnostics.Debug.WriteLine($"مجلد البرنامج: {executableDirectory}");
                System.Diagnostics.Debug.WriteLine($"مجلد البيانات: {dataFolderPath}");
                System.Diagnostics.Debug.WriteLine($"ملف البيانات: {dataFilePath}");

                // إنشاء مجلد البيانات إذا لم يكن موجوداً
                if (!Directory.Exists(dataFolderPath))
                {
                    Directory.CreateDirectory(dataFolderPath);
                    System.Diagnostics.Debug.WriteLine("تم إنشاء مجلد البيانات");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("مجلد البيانات موجود بالفعل");
                }

                // تهيئة المكونات الأساسية أولاً
                InitializeComponent();

                // إضافة معالجات الأحداث
                this.Loaded += MainWindow_Loaded;
                this.Activated += MainWindow_Activated;
                this.Closing += MainWindow_Closing;

                // تهيئة البيانات
                InitializeContentItems();

                // تهيئة الشبكة
                InitializeNetwork();

                // تحميل API key وتهيئة OpenAI
                LoadApiKey();
                InitializeOpenAI();

                // بدء المؤقت
                StartTimer();

                // تطبيق لون شريط العنوان بطرق متعددة
                this.Loaded += (s, e) => ApplyTitleBarColor();
                this.Activated += (s, e) => ApplyTitleBarColor();
                this.SourceInitialized += (s, e) => ApplyTitleBarColor();

                // إنشاء ملف README للنقل بين الأجهزة (معطل)
                // CreatePortabilityReadme();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة:\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}",
                    "خطأ في التهيئة", MessageBoxButton.OK, MessageBoxImage.Error);

                // محاولة إغلاق التطبيق بأمان
                Application.Current?.Shutdown();
            }
        }

        #region دالة شريط العنوان المنفصلة

        /// <summary>
        /// دالة منفصلة لتطبيق لون شريط العنوان
        /// تستخدم نفس لون زر التوضيح (#161642)
        /// محسنة للعمل على جميع الأجهزة وإصدارات Windows
        /// </summary>
        private void ApplyTitleBarColor()
        {
            try
            {
                var hwnd = new WindowInteropHelper(this).Handle;
                if (hwnd == IntPtr.Zero)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم الحصول على handle النافذة");
                    return;
                }

                // التحقق من دعم النظام لميزات شريط العنوان
                bool isWindows10OrLater = IsWindows10OrLater();
                bool supportsTitleBar = SupportsTitleBarCustomization();
                bool isCompositionEnabled = false;

                try
                {
                    DwmIsCompositionEnabled(out isCompositionEnabled);
                }
                catch
                {
                    isCompositionEnabled = false;
                }

                System.Diagnostics.Debug.WriteLine($"📊 فحص دعم النظام:");
                System.Diagnostics.Debug.WriteLine($"   - Windows 10 أو أحدث: {isWindows10OrLater}");
                System.Diagnostics.Debug.WriteLine($"   - دعم تخصيص شريط العنوان: {supportsTitleBar}");
                System.Diagnostics.Debug.WriteLine($"   - DWM Composition مفعل: {isCompositionEnabled}");

                if (!isWindows10OrLater || !supportsTitleBar)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ النظام لا يدعم تخصيص شريط العنوان المتقدم");
                }

                // محاولة تطبيق اللون بطرق متعددة
                bool colorApplied = false;

                // الطريقة الأولى: استخدام DWMWA_CAPTION_COLOR (Windows 11)
                try
                {
                    int titleBarColor = 0x421616; // BGR format for #161642
                    int result = DwmSetWindowAttribute(hwnd, DWMWA_CAPTION_COLOR, ref titleBarColor, sizeof(int));
                    if (result == 0)
                    {
                        colorApplied = true;
                        System.Diagnostics.Debug.WriteLine("✅ تم تطبيق لون شريط العنوان بالطريقة الأولى (CAPTION_COLOR)");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ فشل في الطريقة الأولى، كود الخطأ: {result}");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في الطريقة الأولى: {ex.Message}");
                }

                // الطريقة الثانية: استخدام Dark Mode (Windows 10/11)
                if (!colorApplied)
                {
                    try
                    {
                        int darkMode = 1; // تفعيل الوضع الداكن
                        int result = DwmSetWindowAttribute(hwnd, DWMWA_USE_IMMERSIVE_DARK_MODE, ref darkMode, sizeof(int));
                        if (result == 0)
                        {
                            colorApplied = true;
                            System.Diagnostics.Debug.WriteLine("✅ تم تطبيق الوضع الداكن لشريط العنوان");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ فشل في تطبيق الوضع الداكن، كود الخطأ: {result}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في تطبيق الوضع الداكن: {ex.Message}");
                    }
                }

                // الطريقة الثالثة: تطبيق لون بديل للنافذة نفسها
                if (!colorApplied)
                {
                    try
                    {
                        // تطبيق لون خلفية النافذة
                        this.Background = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42));
                        System.Diagnostics.Debug.WriteLine("✅ تم تطبيق لون خلفية النافذة كبديل");
                        colorApplied = true;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في تطبيق لون خلفية النافذة: {ex.Message}");
                    }
                }

                // تقرير النتيجة النهائية
                if (colorApplied)
                {
                    System.Diagnostics.Debug.WriteLine("🎨 تم تطبيق لون شريط العنوان بنجاح");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ فشل في تطبيق لون شريط العنوان بجميع الطرق");
                }

                // معلومات إضافية للتشخيص
                System.Diagnostics.Debug.WriteLine($"📊 معلومات النظام:");
                System.Diagnostics.Debug.WriteLine($"   - إصدار Windows: {Environment.OSVersion}");
                System.Diagnostics.Debug.WriteLine($"   - Handle النافذة: {hwnd}");
                System.Diagnostics.Debug.WriteLine($"   - DWM مفعل: {isCompositionEnabled}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام في تطبيق لون شريط العنوان: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"   Stack Trace: {ex.StackTrace}");
            }
        }

        #endregion

        #region دالة التحقق من إصدار Windows

        /// <summary>
        /// دالة للتحقق من إصدار Windows ودعم ميزات شريط العنوان
        /// </summary>
        private bool IsWindows10OrLater()
        {
            try
            {
                var version = Environment.OSVersion.Version;
                // Windows 10 = 10.0, Windows 11 = 10.0 (Build 22000+)
                return version.Major >= 10;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// دالة للتحقق من دعم ميزات شريط العنوان المتقدمة
        /// </summary>
        private bool SupportsTitleBarCustomization()
        {
            try
            {
                // التحقق من وجود dwmapi.dll
                var dwmModule = GetModuleHandle("dwmapi.dll");
                if (dwmModule == IntPtr.Zero)
                {
                    return false;
                }

                // التحقق من وجود الدالة المطلوبة
                var procAddress = GetProcAddress(dwmModule, "DwmSetWindowAttribute");
                return procAddress != IntPtr.Zero;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region دالة إنشاء ملف README للنقل بين الأجهزة

        /// <summary>
        /// دالة منفصلة لإنشاء ملف README يوضح كيفية نقل البرنامج بين الأجهزة
        /// </summary>
        private void CreatePortabilityReadme()
        {
            try
            {
                var executablePath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                var executableDirectory = Path.GetDirectoryName(executablePath);
                var readmePath = Path.Combine(executableDirectory, "نقل البرنامج بين الأجهزة.txt");

                if (!File.Exists(readmePath))
                {
                    var readmeContent = @"🎯 Inzo IB v7.4 - دليل نقل البرنامج بين الأجهزة
===============================================

📁 هيكل ملفات البرنامج:
├── InzoIB_v7.4_Simple.exe          # ملف البرنامج الرئيسي
├── InzoIB_v7.4_Simple.dll          # مكتبات البرنامج
├── Data/                           # مجلد البيانات (يحتوي على جميع المحتويات)
│   ├── content_data.json           # ملف البيانات الرئيسي
│   └── Attachments/                # مجلد الملفات المرفقة
│       ├── 20241215120000_file1.pdf
│       ├── 20241215120001_image1.jpg
│       └── ...
├── نقل البرنامج بين الأجهزة.txt      # هذا الملف
└── ملفات أخرى...

🔄 كيفية نقل البرنامج إلى جهاز آخر:

1️⃣ نسخ المجلد كاملاً:
   • انسخ المجلد الذي يحتوي على البرنامج بالكامل
   • تأكد من نسخ مجلد ""Data"" مع جميع محتوياته
   • تأكد من نسخ مجلد ""Attachments"" مع جميع الملفات المرفقة

2️⃣ لصق المجلد في الجهاز الجديد:
   • الصق المجلد في أي مكان تريده في الجهاز الجديد
   • لا تحتاج لتثبيت أو إعداد إضافي

3️⃣ تشغيل البرنامج:
   • انقر مرتين على InzoIB_v7.4_Simple.exe
   • سيتم تحميل جميع البيانات والملفات تلقائياً

✅ مميزات النقل:
• البرنامج محمول (Portable) - لا يحتاج تثبيت
• جميع البيانات محفوظة في مجلد البرنامج
• الملفات المرفقة محفوظة محلياً
• لا يعتمد على مسارات مطلقة
• يعمل على أي جهاز Windows

⚠️ ملاحظات مهمة:
• تأكد من نسخ مجلد ""Data"" كاملاً
• لا تحذف أو تنقل ملفات من مجلد ""Attachments""
• احتفظ بنسخة احتياطية من مجلد البرنامج
• البرنامج يحفظ البيانات تلقائياً عند كل تغيير

🎊 استمتع باستخدام البرنامج على أي جهاز!

تاريخ الإنشاء: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + @"
إصدار البرنامج: Inzo IB v7.4";

                    File.WriteAllText(readmePath, readmeContent, System.Text.Encoding.UTF8);
                    System.Diagnostics.Debug.WriteLine($"تم إنشاء ملف README في: {readmePath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء ملف README: {ex.Message}");
            }
        }

        #endregion

        #region دالة التحقق من سلامة البيانات عند النقل

        /// <summary>
        /// دالة منفصلة للتحقق من سلامة البيانات والملفات المرفقة
        /// تضمن عمل البرنامج بشكل صحيح بعد النقل بين الأجهزة
        /// </summary>
        private void ValidateDataIntegrity()
        {
            try
            {
                if (contentItems == null || contentItems.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("لا توجد بيانات للتحقق منها");
                    return;
                }

                int validFiles = 0;
                int invalidFiles = 0;
                int totalFiles = 0;

                foreach (var item in contentItems)
                {
                    if (item.AttachedFiles != null && item.AttachedFiles.Count > 0)
                    {
                        for (int i = item.AttachedFiles.Count - 1; i >= 0; i--)
                        {
                            totalFiles++;
                            var filePath = item.AttachedFiles[i];

                            // التحقق من وجود الملف
                            if (File.Exists(filePath))
                            {
                                validFiles++;
                                System.Diagnostics.Debug.WriteLine($"ملف صالح: {Path.GetFileName(filePath)}");
                            }
                            else
                            {
                                invalidFiles++;
                                System.Diagnostics.Debug.WriteLine($"ملف مفقود: {Path.GetFileName(filePath)}");

                                // محاولة البحث عن الملف في مجلد Attachments
                                var fileName = Path.GetFileName(filePath);
                                var attachmentsFolder = Path.Combine(dataFolderPath, "Attachments");
                                var possiblePath = Path.Combine(attachmentsFolder, fileName);

                                if (File.Exists(possiblePath))
                                {
                                    // تحديث المسار إلى المسار الصحيح
                                    item.AttachedFiles[i] = possiblePath;
                                    validFiles++;
                                    invalidFiles--;
                                    System.Diagnostics.Debug.WriteLine($"تم إصلاح مسار الملف: {fileName}");
                                }
                            }
                        }
                    }
                }

                // إحصائيات التحقق
                System.Diagnostics.Debug.WriteLine($"=== تقرير سلامة البيانات ===");
                System.Diagnostics.Debug.WriteLine($"إجمالي العناصر: {contentItems.Count}");
                System.Diagnostics.Debug.WriteLine($"إجمالي الملفات: {totalFiles}");
                System.Diagnostics.Debug.WriteLine($"ملفات صالحة: {validFiles}");
                System.Diagnostics.Debug.WriteLine($"ملفات مفقودة: {invalidFiles}");

                if (invalidFiles > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ تحذير: {invalidFiles} ملف مفقود");
                }
                else if (totalFiles > 0)
                {
                    System.Diagnostics.Debug.WriteLine("✅ جميع الملفات سليمة");
                }

                // حفظ البيانات المحدثة إذا تم إصلاح أي مسارات
                if (validFiles > 0)
                {
                    SaveData();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من سلامة البيانات: {ex.Message}");
            }
        }

        #endregion

        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // تحميل المحتوى بعد تحميل النافذة
                LoadCurrentSectionContent();

                // تعيين لون زر القسم الافتراضي (توضيح)
                UpdateSectionButtonColors(BtnTawdeeh);

                // عرض رسالة النجاح
                if (StatusText != null)
                {
                    StatusText.Text = "✅ تم تشغيل Inzo IB v7.4 بنجاح";
                }

                // التأكد من ظهور النافذة
                this.WindowState = WindowState.Normal;
                this.Activate();
                this.Focus();

                // جعل النافذة في المقدمة مؤقتاً
                this.Topmost = true;
                this.Topmost = false;

                // التأكد من إظهار جميع أزرار المهام وإعطائها أولوية عالية
                if (TaskEditButton != null)
                {
                    TaskEditButton.Visibility = Visibility.Visible;
                    TaskEditButton.IsEnabled = true;
                    Panel.SetZIndex(TaskEditButton, 1000);
                }
                if (TaskCopyButton != null)
                {
                    TaskCopyButton.Visibility = Visibility.Visible;
                    TaskCopyButton.IsEnabled = true;
                    Panel.SetZIndex(TaskCopyButton, 1000);
                }
                if (TaskDeleteButton != null)
                {
                    TaskDeleteButton.Visibility = Visibility.Visible;
                    TaskDeleteButton.IsEnabled = true;
                    Panel.SetZIndex(TaskDeleteButton, 1000);
                }
                if (TaskFilesButton != null)
                {
                    TaskFilesButton.Visibility = Visibility.Visible;
                    TaskFilesButton.IsEnabled = true;
                    Panel.SetZIndex(TaskFilesButton, 1000);
                }
                if (TaskMoveUpButton != null)
                {
                    TaskMoveUpButton.Visibility = Visibility.Visible;
                    TaskMoveUpButton.IsEnabled = true;
                    Panel.SetZIndex(TaskMoveUpButton, 1000);
                }
                if (TaskMoveDownButton != null)
                {
                    TaskMoveDownButton.Visibility = Visibility.Visible;
                    TaskMoveDownButton.IsEnabled = true;
                    Panel.SetZIndex(TaskMoveDownButton, 1000);
                }
                if (TaskDividerButton != null)
                {
                    TaskDividerButton.Visibility = Visibility.Visible;
                    TaskDividerButton.IsEnabled = true;
                    Panel.SetZIndex(TaskDividerButton, 1000);
                }
                if (TaskAddButton != null)
                {
                    TaskAddButton.Visibility = Visibility.Visible;
                    TaskAddButton.IsEnabled = true;
                    Panel.SetZIndex(TaskAddButton, 1000);
                }

                // فرض إظهار الأزرار مرة أخرى
                ForceShowTaskButtons();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل النافذة:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void MainWindow_Activated(object sender, EventArgs e)
        {
            try
            {
                // التأكد من أن النافذة مرئية
                this.Visibility = Visibility.Visible;
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء البسيطة في التفعيل
                System.Diagnostics.Debug.WriteLine($"خطأ في تفعيل النافذة: {ex.Message}");
            }
        }

        private void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // حفظ البيانات قبل إغلاق البرنامج
                SaveData();

                // إيقاف المؤقت
                if (timer != null)
                {
                    timer.Stop();
                    timer = null;
                }

                // إيقاف مؤقت فحص الشبكة
                if (networkCheckTimer != null)
                {
                    networkCheckTimer.Stop();
                    networkCheckTimer = null;
                }

                // تنظيف موارد HttpClient
                httpClient?.Dispose();

                // رسالة تأكيد الحفظ
                System.Diagnostics.Debug.WriteLine("تم حفظ جميع البيانات وتنظيف الموارد بنجاح قبل إغلاق البرنامج");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ البيانات عند الإغلاق: {ex.Message}");
            }
        }

        private void ForceShowTaskButtons()
        {
            try
            {
                // فرض إظهار جميع أزرار المهام
                var buttons = new[] { TaskEditButton, TaskCopyButton, TaskDeleteButton, TaskFilesButton,
                                     TaskMoveUpButton, TaskMoveDownButton, TaskDividerButton, TaskAddButton };

                foreach (var button in buttons)
                {
                    if (button != null)
                    {
                        button.Visibility = Visibility.Visible;
                        button.IsEnabled = true;
                        Panel.SetZIndex(button, 1000);
                        button.BringIntoView();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في فرض إظهار الأزرار: {ex.Message}");
            }
        }

        private void InitializeContentItems()
        {
            try
            {
                // محاولة تحميل البيانات المحفوظة أولاً
                LoadData();

                // التحقق من سلامة البيانات والملفات المرفقة
                ValidateDataIntegrity();

                // إذا لم تكن هناك بيانات محفوظة، إنشاء بيانات افتراضية
                if (contentItems == null || contentItems.Count == 0)
                {
                    contentItems = new List<ContentItem>
                    {
                        new ContentItem
                        {
                            Section = "توضيح",
                            Title = "مرحباً بك في Inzo IB v7.4",
                            Content = "نظام إدارة المحتوى المتطور مع واجهة عربية كاملة\n\n🎯 الميزات الرئيسية:\n• شريط مهام محسن مع أدوات متقدمة\n• بحث سريع وذكي\n• إدارة ملفات متطورة\n• واجهة سهلة الاستخدام",
                            CreatedDate = DateTime.Now
                        },
                        new ContentItem
                        {
                            Section = "توضيح",
                            Title = "الميزات المتوفرة",
                            Content = "📋 الميزات المتوفرة:\n• 13 قسم للمحتوى مع أيقونات\n• بحث فوري عبر جميع المحتوى\n• إضافة وتعديل وحذف المحتوى\n• واجهة عربية مع دعم RTL\n• حفظ تلقائي للبيانات",
                            CreatedDate = DateTime.Now
                        }
                    };

                    // حفظ البيانات الافتراضية
                    SaveData();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة البيانات: {ex.Message}");

                // في حالة الخطأ، إنشاء قائمة فارغة
                contentItems = new List<ContentItem>();
            }
        }

        private void StartTimer()
        {
            try
            {
                timer = new DispatcherTimer();
                timer.Interval = TimeSpan.FromSeconds(1);
                timer.Tick += (s, e) =>
                {
                    if (TimeText != null)
                    {
                        TimeText.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
                    }
                };
                timer.Start();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في بدء المؤقت: {ex.Message}");
            }
        }

        private void NavigateToSection(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button != null)
            {
                string sectionName = button.Content.ToString().Substring(2); // Remove emoji
                currentSection = sectionName.Trim();
                StatusText.Text = $"تم الانتقال إلى قسم: {currentSection}";

                // تحديث ألوان أزرار الأقسام
                UpdateSectionButtonColors(button);

                // التمرير إلى الأعلى قبل تحميل المحتوى
                ContentScrollViewer.ScrollToTop();

                // Update content based on section
                LoadCurrentSectionContent();
            }
        }

        #region Network Integration

        private void InitializeNetwork()
        {
            try
            {
                // تكوين HttpClient
                httpClient.Timeout = TimeSpan.FromSeconds(30);
                httpClient.DefaultRequestHeaders.Add("User-Agent", "InzoIB/7.4");

                // فحص الاتصال الأولي
                _ = Task.Run(async () => await CheckInternetConnection());

                // بدء مؤقت فحص الشبكة كل 30 ثانية
                networkCheckTimer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromSeconds(30)
                };
                networkCheckTimer.Tick += NetworkCheckTimer_Tick;
                networkCheckTimer.Start();

                System.Diagnostics.Debug.WriteLine("✅ تم تهيئة الشبكة بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة الشبكة: {ex.Message}");
            }
        }

        private async void NetworkCheckTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                await CheckInternetConnection();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في فحص الشبكة الدوري: {ex.Message}");
            }
        }

        private async Task<bool> CheckInternetConnection()
        {
            try
            {
                // فحص الاتصال بـ Google DNS
                using (var ping = new Ping())
                {
                    var reply = await ping.SendPingAsync("*******", 5000);
                    isInternetConnected = reply.Status == IPStatus.Success;
                }

                // تحديث شريط الحالة
                UpdateNetworkStatus();

                return isInternetConnected;
            }
            catch (Exception ex)
            {
                isInternetConnected = false;
                UpdateNetworkStatus();
                System.Diagnostics.Debug.WriteLine($"خطأ في فحص الاتصال: {ex.Message}");
                return false;
            }
        }

        private void UpdateNetworkStatus()
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    var networkStatus = isInternetConnected ? "🌐 متصل" : "🔴 غير متصل";
                    var currentStatus = StatusText.Text;

                    // إضافة حالة الشبكة إلى شريط الحالة
                    if (!currentStatus.Contains("🌐") && !currentStatus.Contains("🔴"))
                    {
                        StatusText.Text = $"{currentStatus} | {networkStatus}";
                    }
                    else
                    {
                        // استبدال حالة الشبكة الحالية
                        var parts = currentStatus.Split('|');
                        if (parts.Length > 1)
                        {
                            StatusText.Text = $"{parts[0].Trim()} | {networkStatus}";
                        }
                        else
                        {
                            StatusText.Text = $"{currentStatus} | {networkStatus}";
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث حالة الشبكة: {ex.Message}");
            }
        }

        public async Task<string> FetchDataFromUrl(string url)
        {
            try
            {
                if (!isInternetConnected)
                {
                    return "❌ لا يوجد اتصال بالإنترنت";
                }

                var response = await httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                return content;
            }
            catch (HttpRequestException ex)
            {
                return $"❌ خطأ في الشبكة: {ex.Message}";
            }
            catch (TaskCanceledException)
            {
                return "❌ انتهت مهلة الاتصال";
            }
            catch (Exception ex)
            {
                return $"❌ خطأ غير متوقع: {ex.Message}";
            }
        }

        public async Task<T> FetchJsonFromUrl<T>(string url) where T : class
        {
            try
            {
                var jsonContent = await FetchDataFromUrl(url);

                if (jsonContent.StartsWith("❌"))
                {
                    return null;
                }

                return System.Text.Json.JsonSerializer.Deserialize<T>(jsonContent);
            }
            catch (System.Text.Json.JsonException ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحليل JSON: {ex.Message}");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب JSON: {ex.Message}");
                return null;
            }
        }

        public async Task<bool> PostDataToUrl(string url, object data)
        {
            try
            {
                if (!isInternetConnected)
                {
                    return false;
                }

                var jsonContent = System.Text.Json.JsonSerializer.Serialize(data);
                var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

                var response = await httpClient.PostAsync(url, content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إرسال البيانات: {ex.Message}");
                return false;
            }
        }

        public bool IsInternetConnected => isInternetConnected;



        private void ShowApiKeyDialog()
        {
            // استخدام النافذة البديلة البسيطة مباشرة لتجنب أي مشاكل
            ShowSimpleApiKeyDialog();
        }

        private void ShowSimpleApiKeyDialog()
        {
            try
            {
                // فتح نافذة إدخال API مباشرة بدون رسالة منبثقة
                var apiKeyDialog = new SimpleApiKeyDialog(openAIApiKey);
                apiKeyDialog.Owner = this;

                if (apiKeyDialog.ShowDialog() == true)
                {
                    var newApiKey = apiKeyDialog.ApiKey;

                    if (!string.IsNullOrEmpty(newApiKey))
                    {
                        openAIApiKey = newApiKey;

                        // حفظ المفتاح
                        SaveApiKey(newApiKey);

                        // إعادة تهيئة OpenAI
                        InitializeOpenAI();

                        StatusText.Text = "✅ تم حفظ مفتاح OpenAI GPT-4o! يمكنك الآن استخدام البحث الذكي المتقدم";

                        MessageBox.Show("✅ تم حفظ مفتاح OpenAI GPT-4o بنجاح!\n\n🤖 يمكنك الآن استخدام البحث الذكي المتقدم في البرنامج.\n\n⚡ مميزات GPT-4o:\n• تحليل أعمق وأذكى للمحتوى\n• إجابات أكثر دقة وتفصيلاً\n• فهم متقدم للغة العربية\n\n🔍 جرب البحث عن أي موضوع وستحصل على ردود ذكية متطورة!",
                            "تم الحفظ بنجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    StatusText.Text = "ℹ️ تم إلغاء إعدادات API";
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = "❌ خطأ في إعدادات API";
                MessageBox.Show($"❌ خطأ في إعدادات API:\n\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"خطأ في ShowSimpleApiKeyDialog: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// معالج منفصل لفتح إعدادات الذكاء الاصطناعي مباشرة بدون اختبار الشبكة
        /// </summary>
        private void AISettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusText.Text = "🤖 فتح إعدادات الذكاء الاصطناعي...";

                // فتح نافذة إعدادات API مباشرة
                ShowSimpleApiKeyDialog();
            }
            catch (Exception ex)
            {
                StatusText.Text = "❌ خطأ في فتح إعدادات الذكاء الاصطناعي";
                MessageBox.Show($"❌ خطأ في فتح إعدادات الذكاء الاصطناعي:\n\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"خطأ في AISettingsButton_Click: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
            }
        }

        private void SaveApiKey(string apiKey)
        {
            try
            {
                // إنشاء مجلد Data إذا لم يكن موجوداً
                var dataFolder = Path.GetDirectoryName(apiKeyFilePath);
                if (!Directory.Exists(dataFolder))
                {
                    Directory.CreateDirectory(dataFolder);
                }

                // تشفير وحفظ المفتاح
                var encryptedKey = EncryptString(apiKey);
                File.WriteAllText(apiKeyFilePath, encryptedKey);

                System.Diagnostics.Debug.WriteLine("✅ تم حفظ API key بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ API key: {ex.Message}");
            }
        }

        private string EncryptString(string text)
        {
            try
            {
                var data = System.Text.Encoding.UTF8.GetBytes(text);
                var encrypted = System.Security.Cryptography.ProtectedData.Protect(data, null, System.Security.Cryptography.DataProtectionScope.CurrentUser);
                return Convert.ToBase64String(encrypted);
            }
            catch
            {
                return text; // في حالة فشل التشفير، احفظ النص كما هو
            }
        }



        #endregion

        #region OpenAI Integration

        private void LoadApiKey()
        {
            try
            {
                if (File.Exists(apiKeyFilePath))
                {
                    var encryptedKey = File.ReadAllText(apiKeyFilePath);
                    openAIApiKey = DecryptString(encryptedKey);
                    System.Diagnostics.Debug.WriteLine("✅ تم تحميل API key من الملف");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على ملف API key");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل API key: {ex.Message}");
            }
        }

        private string DecryptString(string encryptedText)
        {
            try
            {
                var data = Convert.FromBase64String(encryptedText);
                var decrypted = System.Security.Cryptography.ProtectedData.Unprotect(data, null, System.Security.Cryptography.DataProtectionScope.CurrentUser);
                return System.Text.Encoding.UTF8.GetString(decrypted);
            }
            catch
            {
                return encryptedText; // في حالة فشل فك التشفير، أرجع النص كما هو
            }
        }

        private void InitializeOpenAI()
        {
            try
            {
                if (!string.IsNullOrEmpty(openAIApiKey) && openAIApiKey != "YOUR_OPENAI_API_KEY_HERE")
                {
                    var openAIClientMain = new OpenAIClient(openAIApiKey);
                    openAIClient = openAIClientMain.GetChatClient("gpt-4o");
                    System.Diagnostics.Debug.WriteLine("✅ تم تهيئة OpenAI بنجاح");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم تعيين مفتاح OpenAI API");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة OpenAI: {ex.Message}");
            }
        }

        private async Task<string> GenerateAIResponse(string query, List<ContentItem> relevantContent)
        {
            try
            {
                if (openAIClient == null)
                {
                    return "🤖 الذكاء الاصطناعي GPT-4o غير متاح حالياً. يرجى التحقق من إعدادات API.";
                }

                if (!isInternetConnected)
                {
                    return "🤖 الذكاء الاصطناعي GPT-4o يتطلب اتصال بالإنترنت.";
                }

                // إنشاء سياق من المحتوى الموجود
                var context = BuildContextFromContent(relevantContent);

                var messages = new List<ChatMessage>
                {
                    ChatMessage.CreateSystemMessage($@"أنت مساعد ذكي متقدم يعمل بنموذج GPT-4o لبرنامج Inzo IB لإدارة المحتوى.
مهمتك هي مساعدة المستخدم بناءً على المحتوى الموجود في البرنامج وتقديم تحليل عميق ومفيد.

السياق المتاح:
{context}

قواعد الإجابة المتقدمة:
1. أجب باللغة العربية الفصحى مع مراعاة الوضوح والبساطة
2. حلل المعلومات الموجودة في السياق بعمق واستخرج الأنماط والعلاقات
3. قدم إجابات شاملة ومفصلة مع أمثلة عملية
4. اقترح حلول إبداعية ومبتكرة للمشاكل
5. استخدم الرموز التعبيرية بذكاء لتحسين التواصل
6. إذا لم تجد معلومات كافية، اقترح خطة عمل مفصلة لإضافة المحتوى المطلوب
7. قدم نصائح متقدمة لتحسين إدارة المحتوى والإنتاجية
8. اربط المعلومات ببعضها البعض لتقديم رؤى أعمق"),

                    ChatMessage.CreateUserMessage($"السؤال: {query}")
                };

                var chatCompletionOptions = new ChatCompletionOptions
                {
                    Temperature = 0.8f,  // زيادة الإبداع قليلاً لاستغلال قوة GPT-4o
                    TopP = 0.95f         // تحسين جودة الإجابات
                };

                var response = await openAIClient.CompleteChatAsync(messages, chatCompletionOptions);
                return response.Value.Content[0].Text;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في استدعاء OpenAI: {ex.Message}");
                return $"🤖 عذراً، حدث خطأ في الذكاء الاصطناعي GPT-4o: {ex.Message}";
            }
        }

        private string BuildContextFromContent(List<ContentItem> content)
        {
            if (content == null || content.Count == 0)
                return "لا يوجد محتوى متاح في البرنامج.";

            var contextBuilder = new System.Text.StringBuilder();
            contextBuilder.AppendLine("المحتوى المتاح في البرنامج:");

            foreach (var item in content.Take(10)) // أخذ أول 10 عناصر لتجنب تجاوز حد الرموز
            {
                contextBuilder.AppendLine($"\n📁 القسم: {item.Section}");
                if (!string.IsNullOrEmpty(item.Title))
                    contextBuilder.AppendLine($"📝 العنوان: {item.Title}");
                if (!string.IsNullOrEmpty(item.Content))
                    contextBuilder.AppendLine($"📄 المحتوى: {item.Content.Substring(0, Math.Min(200, item.Content.Length))}...");
                if (item.AttachedFiles.Any())
                    contextBuilder.AppendLine($"📎 ملفات مرفقة: {item.AttachedFiles.Count} ملف");
                contextBuilder.AppendLine("---");
            }

            return contextBuilder.ToString();
        }

        #endregion

        #region دالة تحديث ألوان أزرار الأقسام

        /// <summary>
        /// دالة منفصلة لتحديث ألوان أزرار الأقسام
        /// تجعل القسم النشط بلون مميز وباقي الأقسام باللون العادي
        /// </summary>
        private void UpdateSectionButtonColors(Button activeButton)
        {
            try
            {
                // اللون العادي للأزرار غير النشطة
                var normalColor = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)); // #161642

                // اللون المميز للزر النشط (أخضر داكن)
                var activeColor = new SolidColorBrush(Color.FromRgb(0x2E, 0x7D, 0x32)); // #2E7D32

                // قائمة جميع أزرار الأقسام
                var sectionButtons = new List<Button>
                {
                    BtnTawdeeh, BtnRodod, BtnOmala, BtnFaezeen, BtnMosabakat,
                    BtnWokala, BtnSahbWaEeeda, BtnInzoIB, BtnNasekh,
                    BtnTaaweedWaTadkeek, BtnManshoorat, BtnSowar, BtnMeeting
                };

                // إعادة تعيين جميع الأزرار للون العادي
                foreach (var btn in sectionButtons)
                {
                    if (btn != null)
                    {
                        btn.Background = normalColor;
                    }
                }

                // تعيين اللون المميز للزر النشط
                if (activeButton != null)
                {
                    activeButton.Background = activeColor;
                }

                System.Diagnostics.Debug.WriteLine($"تم تحديث لون زر القسم النشط: {currentSection}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث ألوان أزرار الأقسام: {ex.Message}");
            }
        }

        #endregion

        private void LoadCurrentSectionContent()
        {
            try
            {
                if (ContentPanel == null || contentItems == null)
                {
                    return;
                }

                ContentPanel.Children.Clear();
                ClearSelection();

                // الحصول على محتوى القسم الحالي
                var sectionItems = contentItems.FindAll(item => item.Section == currentSection);

                if (sectionItems.Count == 0)
                {
                    // عرض رسالة عدم وجود محتوى
                    ShowEmptyMessage();
                    return;
                }

                // عرض كل عنصر محتوى
                foreach (var item in sectionItems)
                {
                    CreateContentItemUI(item);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل المحتوى: {ex.Message}");

                // محاولة عرض رسالة خطأ بسيطة
                if (StatusText != null)
                {
                    StatusText.Text = "❌ خطأ في تحميل المحتوى";
                }
            }
        }

        private void ShowEmptyMessage()
        {
            var border = new Border
            {
                Background = Brushes.LightYellow,
                BorderBrush = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 10),
                Padding = new Thickness(20)
            };

            var stackPanel = new StackPanel { HorizontalAlignment = HorizontalAlignment.Center };

            var emptyIcon = new TextBlock
            {
                Text = "📝",
                FontSize = 48,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };

            var emptyTitle = new TextBlock
            {
                Text = $"لا يوجد محتوى في قسم {currentSection}",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };

            var emptyMessage = new TextBlock
            {
                Text = "اضغط على زر 'إضافة جديد' لإضافة محتوى جديد إلى هذا القسم",
                FontSize = 14,
                Foreground = Brushes.Gray,
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center
            };

            stackPanel.Children.Add(emptyIcon);
            stackPanel.Children.Add(emptyTitle);
            stackPanel.Children.Add(emptyMessage);

            border.Child = stackPanel;
            ContentPanel.Children.Add(border);
        }

        private void CreateContentItemUI(ContentItem item)
        {
            // إذا كان شريط تقسيم، إنشاء عنصر شريط
            if (item.IsDivider)
            {
                CreateDividerUI(item);
                return;
            }

            var border = new Border
            {
                Background = Brushes.White,
                BorderBrush = Brushes.LightGray,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 15),
                Padding = new Thickness(15),
                Tag = item,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            // إضافة حدث النقر للتحديد
            border.MouseLeftButtonUp += SelectContentItem;

            var stackPanel = new StackPanel();

            // إضافة العنوان فقط إذا لم يكن فارغاً
            if (!string.IsNullOrWhiteSpace(item.Title))
            {
                var titleBlock = new TextBlock
                {
                    Text = item.Title,
                    FontSize = 16,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                    Margin = new Thickness(0, 0, 0, 10)
                };
                stackPanel.Children.Add(titleBlock);
            }

            // عرض الملف المرفق أو المحتوى
            if (item.AttachedFiles.Count > 0)
            {
                // عرض الملف المرفق
                var attachedFile = item.AttachedFiles.First();
                var fileDisplayElement = CreateFileDisplayElement(attachedFile, item);
                stackPanel.Children.Add(fileDisplayElement);
            }
            else if (!string.IsNullOrWhiteSpace(item.Content))
            {
                // عرض المحتوى النصي فقط إذا لم يكن فارغاً
                var contentBlock = new TextBlock
                {
                    Text = item.Content,
                    FontSize = 14,
                    // لون أغمق عندما لا يوجد عنوان، لون عادي عندما يوجد عنوان
                    Foreground = string.IsNullOrWhiteSpace(item.Title) ?
                        new SolidColorBrush(Color.FromRgb(0x2C, 0x2C, 0x2C)) : // لون أغمق (رمادي داكن جداً)
                        Brushes.DarkGray, // اللون العادي
                    TextWrapping = TextWrapping.Wrap,
                    Margin = new Thickness(0, 0, 0, 10)
                };
                stackPanel.Children.Add(contentBlock);
            }
            else if (string.IsNullOrWhiteSpace(item.Title))
            {
                // إذا لم يكن هناك عنوان ولا محتوى، عرض رسالة توضيحية
                var placeholderBlock = new TextBlock
                {
                    Text = "📝 عنصر فارغ - يمكنك إضافة محتوى أو ملف مرفق",
                    FontSize = 12,
                    Foreground = Brushes.Gray,
                    FontStyle = FontStyles.Italic,
                    TextWrapping = TextWrapping.Wrap,
                    Margin = new Thickness(0, 0, 0, 10)
                };
                stackPanel.Children.Add(placeholderBlock);
            }



            border.Child = stackPanel;
            ContentPanel.Children.Add(border);
        }

        private void CreateDividerUI(ContentItem item)
        {
            try
            {
                var dividerContainer = new StackPanel
                {
                    Margin = new Thickness(0, 10, 0, 20),
                    Tag = item
                };

                // إضافة حدث النقر للتحديد
                dividerContainer.MouseLeftButtonUp += SelectContentItem;

            // الشريط الرئيسي
            var dividerBorder = new Border
            {
                Height = 3,
                Background = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                CornerRadius = new CornerRadius(1.5),
                Margin = new Thickness(0, 5, 0, 5)
            };

            // نص الشريط إذا وجد
            if (!string.IsNullOrWhiteSpace(item.DividerText))
            {
                var textContainer = new Border
                {
                    Background = Brushes.White,
                    BorderBrush = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                    BorderThickness = new Thickness(2),
                    CornerRadius = new CornerRadius(15),
                    Padding = new Thickness(15, 5, 15, 5),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 5)
                };

                var textBlock = new TextBlock
                {
                    Text = item.DividerText,
                    FontSize = 12,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                    HorizontalAlignment = HorizontalAlignment.Center
                };

                textContainer.Child = textBlock;
                dividerContainer.Children.Add(textContainer);
            }

                dividerContainer.Children.Add(dividerBorder);

                ContentPanel.Children.Add(dividerContainer);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء شريط التقسيم: {ex.Message}");

                // إنشاء شريط بسيط في حالة الخطأ
                var simpleDivider = new Border
                {
                    Height = 2,
                    Background = Brushes.Gray,
                    Margin = new Thickness(0, 10, 0, 10),
                    Tag = item
                };
                simpleDivider.MouseLeftButtonUp += SelectContentItem;
                ContentPanel.Children.Add(simpleDivider);
            }
        }

        private FrameworkElement CreateFileDisplayElement(string filePath, ContentItem item)
        {
            if (IsImageFile(filePath) && File.Exists(filePath))
            {
                // عرض الصورة
                return CreateImageDisplay(filePath, item);
            }
            else
            {
                // عرض معلومات الملف
                return CreateFileInfoDisplay(filePath, item);
            }
        }

        private FrameworkElement CreateImageDisplay(string filePath, ContentItem item)
        {
            var border = new Border
            {
                BorderBrush = Brushes.LightGray,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 10),
                Background = Brushes.White,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            // استخدام Grid لضمان أبعاد ثابتة للصورة
            var grid = new Grid
            {
                Margin = new Thickness(5)
            };

            // تعريف الأعمدة: النص على اليسار، الصورة على اليمين
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) }); // النص
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(220, GridUnitType.Pixel) }); // الصورة بعرض ثابت

            try
            {
                // نص المحتوى في العمود الأول (اليسار)
                if (!string.IsNullOrWhiteSpace(item.Content))
                {
                    var contentText = new TextBlock
                    {
                        Text = item.Content,
                        FontSize = 14,
                        // لون أغمق عندما لا يوجد عنوان، لون عادي عندما يوجد عنوان
                        Foreground = string.IsNullOrWhiteSpace(item.Title) ?
                            new SolidColorBrush(Color.FromRgb(0x2C, 0x2C, 0x2C)) : // لون أغمق
                            Brushes.DarkGray, // اللون العادي
                        TextWrapping = TextWrapping.Wrap,
                        Margin = new Thickness(5, 5, 10, 5),
                        VerticalAlignment = VerticalAlignment.Top
                    };
                    Grid.SetColumn(contentText, 0);
                    grid.Children.Add(contentText);
                }

                // إنشاء الصورة في العمود الثاني (اليمين) بأبعاد ثابتة
                var image = new Image
                {
                    Width = 200,
                    Height = 150,
                    Stretch = Stretch.UniformToFill,
                    Margin = new Thickness(5),
                    VerticalAlignment = VerticalAlignment.Top,
                    HorizontalAlignment = HorizontalAlignment.Center
                };
                Grid.SetColumn(image, 1);

                var bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new Uri(filePath);
                bitmap.DecodePixelWidth = 200;
                bitmap.DecodePixelHeight = 150;
                bitmap.EndInit();

                image.Source = bitmap;

                // إضافة الصورة إلى العمود الثاني
                grid.Children.Add(image);


            }
            catch
            {
                var errorText = new TextBlock
                {
                    Text = $"❌ خطأ في تحميل الصورة: {Path.GetFileName(filePath)}",
                    FontSize = 14,
                    Foreground = Brushes.Red,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(10)
                };
                Grid.SetColumnSpan(errorText, 2); // يمتد عبر العمودين
                grid.Children.Add(errorText);
            }

            border.Child = grid;

            // إضافة حدث النقر المزدوج لفتح نافذة إدارة الملف
            border.MouseLeftButtonDown += (s, e) =>
            {
                if (e.ClickCount == 2)
                {
                    OpenFileManagementWindow(item);
                }
            };

            return border;
        }

        private FrameworkElement CreateFileInfoDisplay(string filePath, ContentItem item)
        {
            var border = new Border
            {
                BorderBrush = Brushes.LightGray,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 10),
                Background = Brushes.White,
                Padding = new Thickness(15),
                Cursor = System.Windows.Input.Cursors.Hand
            };

            var stackPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            var infoPanel = new StackPanel();

            // نص المحتوى إذا وجد (فقط)
            if (!string.IsNullOrWhiteSpace(item.Content))
            {
                var contentText = new TextBlock
                {
                    Text = item.Content,
                    FontSize = 14,
                    // لون أغمق عندما لا يوجد عنوان، لون عادي عندما يوجد عنوان
                    Foreground = string.IsNullOrWhiteSpace(item.Title) ?
                        new SolidColorBrush(Color.FromRgb(0x2C, 0x2C, 0x2C)) : // لون أغمق
                        Brushes.DarkGray, // اللون العادي
                    TextWrapping = TextWrapping.Wrap,
                    MaxWidth = 300
                };
                infoPanel.Children.Add(contentText);
            }

            // أيقونة الملف
            var fileIcon = new TextBlock
            {
                Text = GetFileIcon(filePath),
                FontSize = 40,
                Margin = new Thickness(15, 0, 0, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            stackPanel.Children.Add(infoPanel);
            stackPanel.Children.Add(fileIcon);

            border.Child = stackPanel;

            // إضافة حدث النقر المزدوج لفتح نافذة إدارة الملف
            border.MouseLeftButtonDown += (s, e) =>
            {
                if (e.ClickCount == 2)
                {
                    OpenFileManagementWindow(item);
                }
            };

            return border;
        }

        private void OpenFileManagementWindow(ContentItem item)
        {
            var fileWindow = new FileAttachmentWindow(item.Title, item.AttachedFiles);
            if (fileWindow.ShowDialog() == true)
            {
                // تحديث الملفات المرفقة
                item.AttachedFiles = fileWindow.AttachedFiles;
                SaveData();
                LoadContent(currentSection);
            }
        }

        private bool IsImageFile(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            return extension == ".jpg" || extension == ".jpeg" || extension == ".png" ||
                   extension == ".gif" || extension == ".bmp" || extension == ".tiff";
        }

        private string GetFileIcon(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            return extension switch
            {
                ".pdf" => "📄",
                ".doc" or ".docx" => "📝",
                ".xls" or ".xlsx" => "📊",
                ".ppt" or ".pptx" => "📋",
                ".txt" => "📃",
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" => "🖼️",
                ".mp4" or ".avi" or ".mkv" or ".mov" => "🎥",
                ".mp3" or ".wav" or ".flac" => "🎵",
                ".zip" or ".rar" or ".7z" => "📦",
                _ => "📁"
            };
        }

        private string GetFileSize(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    var fileInfo = new FileInfo(filePath);
                    var sizeInBytes = fileInfo.Length;

                    if (sizeInBytes < 1024)
                        return $"{sizeInBytes} بايت";
                    else if (sizeInBytes < 1024 * 1024)
                        return $"{sizeInBytes / 1024:F1} كيلوبايت";
                    else if (sizeInBytes < 1024 * 1024 * 1024)
                        return $"{sizeInBytes / (1024 * 1024):F1} ميجابايت";
                    else
                        return $"{sizeInBytes / (1024 * 1024 * 1024):F1} جيجابايت";
                }
                else
                {
                    return "غير موجود";
                }
            }
            catch
            {
                return "غير معروف";
            }
        }

        private void SaveData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"محاولة حفظ {contentItems?.Count ?? 0} عنصر");

                // التأكد من وجود مجلد البيانات
                if (!Directory.Exists(dataFolderPath))
                {
                    Directory.CreateDirectory(dataFolderPath);
                    System.Diagnostics.Debug.WriteLine($"تم إنشاء مجلد البيانات: {dataFolderPath}");
                }

                var options = new JsonSerializerOptions { WriteIndented = true };
                var json = JsonSerializer.Serialize(contentItems, options);
                File.WriteAllText(dataFilePath, json);

                System.Diagnostics.Debug.WriteLine($"تم حفظ {contentItems.Count} عنصر في: {dataFilePath}");
                System.Diagnostics.Debug.WriteLine($"حجم البيانات المحفوظة: {json.Length} حرف");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ البيانات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"مسار الحفظ: {dataFilePath}");
            }
        }

        private void LoadContent(string section)
        {
            try
            {
                ContentPanel.Children.Clear();

                if (contentItems == null)
                {
                    LoadData();
                }

                var sectionItems = contentItems.Where(item => item.Section == section).ToList();

                if (sectionItems.Count == 0)
                {
                    var noContentMessage = new TextBlock
                    {
                        Text = $"لا يوجد محتوى في قسم '{section}' حتى الآن",
                        FontSize = 16,
                        Foreground = Brushes.Gray,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center,
                        Margin = new Thickness(20)
                    };

                    var border = new Border
                    {
                        Background = Brushes.White,
                        BorderBrush = Brushes.LightGray,
                        BorderThickness = new Thickness(1),
                        CornerRadius = new CornerRadius(5),
                        Margin = new Thickness(0, 0, 0, 15),
                        Padding = new Thickness(20),
                        Child = noContentMessage
                    };

                    ContentPanel.Children.Add(border);
                    return;
                }

                foreach (var item in sectionItems)
                {
                    CreateContentItemUI(item);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل المحتوى: {ex.Message}");
            }
        }

        private void LoadData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"محاولة تحميل البيانات من: {dataFilePath}");

                if (File.Exists(dataFilePath))
                {
                    var json = File.ReadAllText(dataFilePath);
                    System.Diagnostics.Debug.WriteLine($"تم قراءة الملف، حجم البيانات: {json.Length} حرف");

                    contentItems = JsonSerializer.Deserialize<List<ContentItem>>(json) ?? new List<ContentItem>();
                    System.Diagnostics.Debug.WriteLine($"تم تحميل {contentItems.Count} عنصر من: {dataFilePath}");
                }
                else
                {
                    contentItems = new List<ContentItem>();
                    System.Diagnostics.Debug.WriteLine($"لم يتم العثور على ملف البيانات في: {dataFilePath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"مسار الملف: {dataFilePath}");
                contentItems = new List<ContentItem>();
            }
        }

        private void SelectContentItem(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            var element = sender as FrameworkElement;
            var item = element?.Tag as ContentItem;

            if (item != null)
            {
                // إلغاء تحديد العناصر السابقة
                ClearSelection();

                // تحديد العنصر الحالي
                selectedItem = item;

                if (item.IsDivider)
                {
                    // تحديد الشريط
                    var stackPanel = sender as StackPanel;
                    if (stackPanel != null)
                    {
                        try
                        {
                            stackPanel.Background = new SolidColorBrush(Color.FromRgb(0xE3, 0xF2, 0xFD));

                            // إضافة حدود حول الشريط بأمان
                            if (stackPanel.Children.Count > 0)
                            {
                                foreach (UIElement child in stackPanel.Children)
                                {
                                    if (child is Border borderChild && borderChild.Height == 3)
                                    {
                                        borderChild.BorderBrush = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42));
                                        borderChild.BorderThickness = new Thickness(1);
                                        break;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"خطأ في تحديد شريط التقسيم: {ex.Message}");
                        }
                    }

                    // تفعيل أزرار محدودة للشريط
                    if (TaskEditButton != null) TaskEditButton.IsEnabled = false;
                    if (TaskCopyButton != null) TaskCopyButton.IsEnabled = false;
                    if (TaskDeleteButton != null) TaskDeleteButton.IsEnabled = true;
                    if (TaskFilesButton != null) TaskFilesButton.IsEnabled = false;
                    if (TaskMoveUpButton != null) TaskMoveUpButton.IsEnabled = true;
                    if (TaskMoveDownButton != null) TaskMoveDownButton.IsEnabled = true;

                    StatusText.Text = $"✅ تم تحديد شريط التقسيم: {(string.IsNullOrWhiteSpace(item.DividerText) ? "بدون نص" : item.DividerText)}";
                }
                else
                {
                    // تحديد المحتوى العادي
                    var border = sender as Border;
                    if (border != null)
                    {
                        border.BorderBrush = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42));
                        border.BorderThickness = new Thickness(3);
                        border.Background = new SolidColorBrush(Color.FromRgb(0xE3, 0xF2, 0xFD));
                    }

                    // تفعيل جميع أزرار شريط المهام
                    if (TaskEditButton != null) TaskEditButton.IsEnabled = true;
                    if (TaskCopyButton != null) TaskCopyButton.IsEnabled = true;
                    if (TaskDeleteButton != null) TaskDeleteButton.IsEnabled = true;
                    if (TaskFilesButton != null) TaskFilesButton.IsEnabled = true;
                    if (TaskMoveUpButton != null) TaskMoveUpButton.IsEnabled = true;
                    if (TaskMoveDownButton != null) TaskMoveDownButton.IsEnabled = true;

                    StatusText.Text = $"✅ تم تحديد: {item.Title}";
                }
            }
        }

        private void ClearSelection()
        {
            try
            {
                selectedItem = null;

                // إلغاء تحديد جميع العناصر
                if (ContentPanel != null)
                {
                    foreach (UIElement element in ContentPanel.Children)
                    {
                        if (element is Border border)
                        {
                            border.BorderBrush = Brushes.LightGray;
                            border.BorderThickness = new Thickness(1);
                            border.Background = Brushes.White;
                        }
                    }
                }

                // تعطيل أزرار شريط المهام
                if (TaskEditButton != null) TaskEditButton.IsEnabled = false;
                if (TaskCopyButton != null) TaskCopyButton.IsEnabled = false;
                if (TaskDeleteButton != null) TaskDeleteButton.IsEnabled = false;
                if (TaskFilesButton != null) TaskFilesButton.IsEnabled = false;
                if (TaskMoveUpButton != null) TaskMoveUpButton.IsEnabled = false;
                if (TaskMoveDownButton != null) TaskMoveDownButton.IsEnabled = false;

                if (StatusText != null)
                {
                    StatusText.Text = "جاهز - اختر عنصر للتعامل معه";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في مسح التحديد: {ex.Message}");
            }
        }

        private void TaskEditButton_Click(object sender, RoutedEventArgs e)
        {
            if (selectedItem != null)
            {
                EditContent(selectedItem);
            }
        }

        private void TaskCopyButton_Click(object sender, RoutedEventArgs e)
        {
            if (selectedItem != null)
            {
                CopyContent(selectedItem);
            }
        }

        private void TaskDeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (selectedItem != null)
            {
                DeleteContent(selectedItem);
            }
        }

        private void TaskFilesButton_Click(object sender, RoutedEventArgs e)
        {
            if (selectedItem != null)
            {
                ManageFiles(selectedItem);
            }
        }

        private void TaskAddButton_Click(object sender, RoutedEventArgs e)
        {
            var inputDialog = new InputDialog(currentSection);
            inputDialog.Owner = this;

            if (inputDialog.ShowDialog() == true)
            {
                var newItem = new ContentItem
                {
                    Section = currentSection,
                    Title = inputDialog.InputTitle,
                    Content = inputDialog.InputContent,
                    CreatedDate = DateTime.Now
                };

                contentItems.Add(newItem);
                SaveData(); // حفظ البيانات فوراً
                LoadCurrentSectionContent();
                StatusText.Text = $"✅ تم إضافة محتوى جديد في قسم: {currentSection}";
            }
        }

        private void TaskDividerButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dividerDialog = new DividerInputDialog();
                dividerDialog.Owner = this;

                if (dividerDialog.ShowDialog() == true)
                {
                    var newDivider = new ContentItem
                    {
                        Section = currentSection,
                        Title = "",
                        Content = "",
                        CreatedDate = DateTime.Now,
                        IsDivider = true,
                        DividerText = dividerDialog.DividerText ?? ""
                    };

                    contentItems.Add(newDivider);
                    SaveData(); // حفظ البيانات فوراً
                    LoadCurrentSectionContent();
                    StatusText.Text = $"✅ تم إضافة شريط تقسيم في قسم: {currentSection}";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة شريط التقسيم: {ex.Message}");
                StatusText.Text = "❌ حدث خطأ في إضافة شريط التقسيم";
            }
        }

        private void TaskSearchBox_GotFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox.Text == "🔍 البحث السريع...")
            {
                textBox.Text = "";
            }
        }

        private void TaskSearchBox_LostFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                textBox.Text = "🔍 البحث السريع...";
            }
        }

        private void TaskSearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox.Text != "🔍 البحث السريع..." && !string.IsNullOrWhiteSpace(textBox.Text))
            {
                SearchContent(textBox.Text);
            }
            else
            {
                // التمرير إلى الأعلى عند إلغاء البحث
                ContentScrollViewer.ScrollToTop();
                LoadCurrentSectionContent();
            }
        }

        private async void SearchContent(string searchTerm)
        {
            ContentPanel.Children.Clear();
            ClearSelection();

            // التمرير إلى الأعلى عند البحث
            ContentScrollViewer.ScrollToTop();

            // البحث التقليدي في جميع المحتويات بما في ذلك شرائط التقسيم
            var searchResults = contentItems.Where(item =>
                item.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                item.Content.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                (item.IsDivider && item.DividerText.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))).ToList();

            // عرض النتائج التقليدية أولاً
            if (searchResults.Count > 0)
            {
                // عنوان النتائج التقليدية
                CreateAISearchHeader("🔍 النتائج المطابقة", searchResults.Count);

                // عرض النتائج مجمعة حسب القسم
                var groupedResults = searchResults.GroupBy(item => item.Section);

                foreach (var group in groupedResults)
                {
                    // عنوان القسم
                    CreateSectionHeader(group.Key, group.Count());

                    // عناصر القسم
                    foreach (var item in group)
                    {
                        CreateSearchResultItem(item, searchTerm);
                    }
                }
            }

            // إضافة البحث الذكي بالذكاء الاصطناعي
            await AddAISearchResults(searchTerm, searchResults);

            // تحديث شريط الحالة
            var totalResults = searchResults.Count;
            var sectionsCount = searchResults.GroupBy(item => item.Section).Count();
            StatusText.Text = $"🔍 البحث عن: {searchTerm} - {totalResults} نتيجة تقليدية في {sectionsCount} قسم + نتائج ذكية";
        }

        private async Task AddAISearchResults(string searchTerm, List<ContentItem> traditionalResults)
        {
            try
            {
                // إضافة مؤشر التحميل
                CreateAILoadingIndicator();

                // الحصول على رد الذكاء الاصطناعي
                var aiResponse = await GenerateAIResponse(searchTerm, contentItems);

                // إزالة مؤشر التحميل
                RemoveAILoadingIndicator();

                // عرض رد الذكاء الاصطناعي
                CreateAIResponseItem(aiResponse, searchTerm);

                // اقتراح محتوى ذي صلة
                var relatedContent = FindRelatedContent(searchTerm, traditionalResults);
                if (relatedContent.Any())
                {
                    CreateAISearchHeader("🤖 محتوى ذو صلة (مقترح بالذكاء الاصطناعي)", relatedContent.Count);
                    foreach (var item in relatedContent.Take(5)) // أقصى 5 اقتراحات
                    {
                        CreateSearchResultItem(item, searchTerm, true);
                    }
                }
            }
            catch (Exception ex)
            {
                RemoveAILoadingIndicator();
                CreateAIErrorItem($"❌ خطأ في البحث الذكي: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث الذكي: {ex.Message}");
            }
        }

        private List<ContentItem> FindRelatedContent(string searchTerm, List<ContentItem> excludeItems)
        {
            var excludeIds = excludeItems.Select(item => item.GetHashCode()).ToHashSet();
            var searchWords = searchTerm.Split(' ', StringSplitOptions.RemoveEmptyEntries);

            return contentItems
                .Where(item => !excludeIds.Contains(item.GetHashCode()) && !item.IsDivider)
                .Where(item => searchWords.Any(word =>
                    item.Title.Contains(word, StringComparison.OrdinalIgnoreCase) ||
                    item.Content.Contains(word, StringComparison.OrdinalIgnoreCase)))
                .OrderByDescending(item => CalculateRelevanceScore(item, searchWords))
                .ToList();
        }

        private int CalculateRelevanceScore(ContentItem item, string[] searchWords)
        {
            int score = 0;
            foreach (var word in searchWords)
            {
                if (item.Title.Contains(word, StringComparison.OrdinalIgnoreCase))
                    score += 3; // العنوان له وزن أكبر
                if (item.Content.Contains(word, StringComparison.OrdinalIgnoreCase))
                    score += 1;
            }
            return score;
        }

        #region AI Search UI Elements

        private void CreateAISearchHeader(string title, int count)
        {
            var headerBorder = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 15, 0, 10),
                Padding = new Thickness(15, 10, 15, 10)
            };

            var headerText = new TextBlock
            {
                Text = $"{title} ({count})",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            headerBorder.Child = headerText;
            ContentPanel.Children.Add(headerBorder);
        }

        private void CreateAIResponseItem(string response, string searchTerm)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(0xE8, 0xF5, 0xE8)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(0x4C, 0xAF, 0x50)),
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(0, 0, 0, 15),
                Padding = new Thickness(20)
            };

            var stackPanel = new StackPanel();

            // عنوان الرد
            var titleBlock = new TextBlock
            {
                Text = "🤖 رد الذكاء الاصطناعي",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(0x2E, 0x7D, 0x32)),
                Margin = new Thickness(0, 0, 0, 10)
            };
            stackPanel.Children.Add(titleBlock);

            // محتوى الرد
            var responseBlock = new TextBlock
            {
                Text = response,
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(0x1B, 0x5E, 0x20)),
                TextWrapping = TextWrapping.Wrap,
                LineHeight = 22
            };
            stackPanel.Children.Add(responseBlock);

            border.Child = stackPanel;
            ContentPanel.Children.Add(border);
        }

        private void CreateAILoadingIndicator()
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(0xFF, 0xF8, 0xE1)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(0xFF, 0xC1, 0x07)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 15),
                Padding = new Thickness(15, 10, 15, 10),
                Tag = "AI_LOADING" // للتعرف عليه لاحقاً
            };

            var stackPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            var loadingText = new TextBlock
            {
                Text = "🤖 جاري البحث بالذكاء الاصطناعي...",
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(0xE6, 0x8A, 0x00)),
                VerticalAlignment = VerticalAlignment.Center
            };

            stackPanel.Children.Add(loadingText);
            border.Child = stackPanel;
            ContentPanel.Children.Add(border);
        }

        private void RemoveAILoadingIndicator()
        {
            var loadingElement = ContentPanel.Children
                .OfType<Border>()
                .FirstOrDefault(b => b.Tag?.ToString() == "AI_LOADING");

            if (loadingElement != null)
            {
                ContentPanel.Children.Remove(loadingElement);
            }
        }

        private void CreateAIErrorItem(string errorMessage)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(0xFF, 0xEB, 0xEE)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(0xF4, 0x43, 0x36)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 15),
                Padding = new Thickness(15, 10, 15, 10)
            };

            var errorText = new TextBlock
            {
                Text = errorMessage,
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(0xC6, 0x28, 0x28)),
                TextWrapping = TextWrapping.Wrap
            };

            border.Child = errorText;
            ContentPanel.Children.Add(border);
        }

        #endregion

        private async void ShowNoSearchResults(string searchTerm)
        {
            var border = new Border
            {
                Background = Brushes.LightYellow,
                BorderBrush = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 10),
                Padding = new Thickness(20)
            };

            var stackPanel = new StackPanel { HorizontalAlignment = HorizontalAlignment.Center };

            var searchIcon = new TextBlock
            {
                Text = "🔍",
                FontSize = 48,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };

            var noResultsTitle = new TextBlock
            {
                Text = $"لم يتم العثور على نتائج للبحث: {searchTerm}",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };

            var suggestion = new TextBlock
            {
                Text = "جرب كلمات مفتاحية أخرى أو تأكد من الإملاء",
                FontSize = 14,
                Foreground = Brushes.Gray,
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center
            };

            stackPanel.Children.Add(searchIcon);
            stackPanel.Children.Add(noResultsTitle);
            stackPanel.Children.Add(suggestion);

            border.Child = stackPanel;
            ContentPanel.Children.Add(border);

            // إضافة البحث الذكي حتى لو لم توجد نتائج تقليدية
            await AddAISearchResults(searchTerm, new List<ContentItem>());

            StatusText.Text = $"🔍 البحث عن: {searchTerm} - لم يتم العثور على نتائج تقليدية + نتائج ذكية";
        }

        private void CreateSectionHeader(string sectionName, int count)
        {
            var headerBorder = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 10, 0, 5),
                Padding = new Thickness(15, 8, 15, 8)
            };

            var headerText = new TextBlock
            {
                Text = $"📂 قسم {sectionName} ({count} نتيجة)",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White
            };

            headerBorder.Child = headerText;
            ContentPanel.Children.Add(headerBorder);
        }

        private void CreateSearchResultItem(ContentItem item, string searchTerm, bool isAISuggestion = false)
        {
            // تحديد الألوان حسب نوع العنصر
            Brush backgroundColor, borderColor;

            if (isAISuggestion)
            {
                backgroundColor = new SolidColorBrush(Color.FromRgb(0xE3, 0xF2, 0xFD));
                borderColor = new SolidColorBrush(Color.FromRgb(0x21, 0x96, 0xF3));
            }
            else if (item.IsDivider)
            {
                backgroundColor = Brushes.LightBlue;
                borderColor = Brushes.Blue;
            }
            else
            {
                backgroundColor = Brushes.LightYellow;
                borderColor = Brushes.Orange;
            }

            var border = new Border
            {
                Background = backgroundColor,
                BorderBrush = borderColor,
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 10),
                Padding = new Thickness(15),
                Tag = item,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            // إضافة حدث النقر للتحديد
            border.MouseLeftButtonUp += SelectContentItem;

            // إضافة حدث الضغط المزدوج للانتقال إلى مكان المحتوى
            border.MouseLeftButtonDown += (s, e) =>
            {
                if (e.ClickCount == 2)
                {
                    NavigateToContentInSection(item);
                }
            };

            var stackPanel = new StackPanel();

            // إضافة مؤشر للمحتوى المقترح بالذكاء الاصطناعي
            if (isAISuggestion)
            {
                var aiIndicator = new TextBlock
                {
                    Text = "🤖 مقترح بالذكاء الاصطناعي",
                    FontSize = 11,
                    FontStyle = FontStyles.Italic,
                    Foreground = new SolidColorBrush(Color.FromRgb(0x21, 0x96, 0xF3)),
                    HorizontalAlignment = HorizontalAlignment.Right,
                    Margin = new Thickness(0, 0, 0, 5)
                };
                stackPanel.Children.Add(aiIndicator);
            }

            // التعامل مع شرائط التقسيم
            if (item.IsDivider)
            {
                // عرض شريط التقسيم
                var dividerIcon = new TextBlock
                {
                    Text = "📏",
                    FontSize = 20,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 5)
                };
                stackPanel.Children.Add(dividerIcon);

                var dividerLabel = new TextBlock
                {
                    Text = "شريط تقسيم",
                    FontSize = 12,
                    FontWeight = FontWeights.Bold,
                    Foreground = Brushes.Blue,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 5)
                };
                stackPanel.Children.Add(dividerLabel);

                if (!string.IsNullOrWhiteSpace(item.DividerText))
                {
                    var dividerTextBlock = new TextBlock
                    {
                        FontSize = 14,
                        FontWeight = FontWeights.Bold,
                        Foreground = Brushes.DarkBlue,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        Margin = new Thickness(0, 0, 0, 10)
                    };
                    dividerTextBlock.Inlines.Add(HighlightSearchTerm(item.DividerText, searchTerm));
                    stackPanel.Children.Add(dividerTextBlock);
                }
            }
            else
            {
                // إضافة العنوان مع تمييز فقط إذا لم يكن فارغاً
                if (!string.IsNullOrWhiteSpace(item.Title))
                {
                    var titleBlock = new TextBlock
                    {
                        FontSize = 16,
                        FontWeight = FontWeights.Bold,
                        Foreground = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                        Margin = new Thickness(0, 0, 0, 10)
                    };
                    titleBlock.Inlines.Add(HighlightSearchTerm(item.Title, searchTerm));
                    stackPanel.Children.Add(titleBlock);
                }
            }

            // عرض الملف المرفق أو المحتوى مع تمييز (فقط للعناصر العادية، ليس شرائط التقسيم)
            if (!item.IsDivider)
            {
                if (item.AttachedFiles.Count > 0)
                {
                    // عرض الملف المرفق مع تمييز البحث
                    var attachedFile = item.AttachedFiles.First();
                    var fileDisplayElement = CreateSearchFileDisplayElement(attachedFile, item, searchTerm);
                    stackPanel.Children.Add(fileDisplayElement);
                }
                else
                {
                    // عرض المحتوى النصي مع تمييز
                    var contentBlock = new TextBlock
                    {
                        FontSize = 14,
                        Foreground = Brushes.DarkOrange,
                        TextWrapping = TextWrapping.Wrap,
                        Margin = new Thickness(0, 0, 0, 10)
                    };
                    contentBlock.Inlines.Add(HighlightSearchTerm(item.Content, searchTerm));
                    stackPanel.Children.Add(contentBlock);
                }
            }

            // معلومات إضافية
            var itemType = item.IsDivider ? "📏 شريط تقسيم" : "📄 محتوى";
            var infoBlock = new TextBlock
            {
                Text = $"📅 {item.CreatedDate:yyyy/MM/dd HH:mm} | 📂 {item.Section} | {itemType}",
                FontSize = 12,
                Foreground = Brushes.Gray,
                Margin = new Thickness(0, 0, 0, 5)
            };

            stackPanel.Children.Add(infoBlock);

            border.Child = stackPanel;
            ContentPanel.Children.Add(border);
        }

        private void NavigateToContentInSection(ContentItem targetItem)
        {
            try
            {
                // تغيير القسم النشط إلى قسم المحتوى المطلوب
                currentSection = targetItem.Section;

                // تحديث ألوان أزرار الأقسام
                UpdateSectionButtonColors(GetSectionButton(targetItem.Section));

                // تحميل محتوى القسم
                LoadCurrentSectionContent();

                // البحث عن العنصر في القائمة المعروضة وتحديده
                this.Dispatcher.BeginInvoke(new Action(() =>
                {
                    foreach (Border border in ContentPanel.Children.OfType<Border>())
                    {
                        if (border.Tag is ContentItem item && item == targetItem)
                        {
                            // تحديد العنصر
                            SelectContentItem(border, null);

                            // التمرير إلى العنصر
                            border.BringIntoView();

                            // تمييز العنصر مؤقتاً
                            var originalBackground = border.Background;
                            border.Background = new SolidColorBrush(Color.FromRgb(0x4C, 0xAF, 0x50)); // أخضر فاتح

                            // إعادة اللون الأصلي بعد ثانيتين
                            var timer = new DispatcherTimer { Interval = TimeSpan.FromSeconds(2) };
                            timer.Tick += (s, e) =>
                            {
                                border.Background = originalBackground;
                                timer.Stop();
                            };
                            timer.Start();

                            break;
                        }
                    }
                }), DispatcherPriority.Background);

                // تحديث شريط الحالة
                StatusText.Text = $"✅ تم الانتقال إلى المحتوى في قسم: {targetItem.Section}";
            }
            catch (Exception ex)
            {
                StatusText.Text = $"❌ خطأ في الانتقال إلى المحتوى: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"خطأ في NavigateToContentInSection: {ex.Message}");
            }
        }

        private Button GetSectionButton(string sectionName)
        {
            return sectionName switch
            {
                "توضيح" => BtnTawdeeh,
                "ردود" => BtnRodod,
                "عملاء" => BtnOmala,
                "فائزين" => BtnFaezeen,
                "مسابقات" => BtnMosabakat,
                "وكلاء" => BtnWokala,
                "السحب والايداع" => BtnSahbWaEeeda,
                "INZO IB" => BtnInzoIB,
                "النسخ" => BtnNasekh,
                "تعويض وتدقيق" => BtnTaaweedWaTadkeek,
                "المنشورات" => BtnManshoorat,
                "صور" => BtnSowar,
                "Meeting" => BtnMeeting,
                _ => BtnTawdeeh
            };
        }

        private FrameworkElement CreateSearchFileDisplayElement(string filePath, ContentItem item, string searchTerm)
        {
            var border = new Border
            {
                BorderBrush = Brushes.Orange,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(3),
                Margin = new Thickness(0, 0, 0, 10),
                Background = Brushes.LightYellow,
                Padding = new Thickness(10)
            };

            var stackPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            // أيقونة الملف
            var fileIcon = new TextBlock
            {
                Text = GetFileIcon(filePath),
                FontSize = 30,
                Margin = new Thickness(0, 0, 10, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            var infoPanel = new StackPanel();

            // اسم الملف مع تمييز
            var fileName = new TextBlock
            {
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.DarkOrange,
                Margin = new Thickness(0, 0, 0, 5)
            };
            fileName.Inlines.Add(HighlightSearchTerm(Path.GetFileName(filePath), searchTerm));

            // معلومات الملف
            var fileInfo = new TextBlock
            {
                Text = $"📏 {GetFileSize(filePath)} | 📂 {Path.GetExtension(filePath).ToUpper()}",
                FontSize = 12,
                Foreground = Brushes.Gray,
                Margin = new Thickness(0, 0, 0, 5)
            };

            // نص المحتوى مع تمييز إذا وجد
            if (!string.IsNullOrWhiteSpace(item.Content))
            {
                var contentText = new TextBlock
                {
                    FontSize = 12,
                    Foreground = Brushes.DarkOrange,
                    TextWrapping = TextWrapping.Wrap,
                    MaxWidth = 300
                };
                contentText.Inlines.Add(HighlightSearchTerm(item.Content, searchTerm));
                infoPanel.Children.Add(contentText);
            }

            infoPanel.Children.Add(fileName);
            infoPanel.Children.Add(fileInfo);

            stackPanel.Children.Add(fileIcon);
            stackPanel.Children.Add(infoPanel);

            border.Child = stackPanel;
            return border;
        }

        private System.Windows.Documents.Span HighlightSearchTerm(string text, string searchTerm)
        {
            var span = new System.Windows.Documents.Span();

            if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(searchTerm))
            {
                span.Inlines.Add(text);
                return span;
            }

            var index = text.IndexOf(searchTerm, StringComparison.OrdinalIgnoreCase);
            if (index == -1)
            {
                span.Inlines.Add(text);
                return span;
            }

            // النص قبل المصطلح
            if (index > 0)
            {
                span.Inlines.Add(text.Substring(0, index));
            }

            // المصطلح المميز
            var highlightedRun = new System.Windows.Documents.Run(text.Substring(index, searchTerm.Length))
            {
                Background = Brushes.Yellow,
                FontWeight = FontWeights.Bold
            };
            span.Inlines.Add(highlightedRun);

            // النص بعد المصطلح
            if (index + searchTerm.Length < text.Length)
            {
                span.Inlines.Add(HighlightSearchTerm(text.Substring(index + searchTerm.Length), searchTerm));
            }

            return span;
        }

        private void AddContent(object sender, RoutedEventArgs e)
        {
            // فتح نافذة إدخال المحتوى الجديد
            var inputDialog = new InputDialog(currentSection);
            inputDialog.Owner = this;

            if (inputDialog.ShowDialog() == true)
            {
                // إنشاء عنصر محتوى جديد
                var newItem = new ContentItem
                {
                    Section = currentSection,
                    Title = inputDialog.InputTitle,
                    Content = inputDialog.InputContent,
                    CreatedDate = DateTime.Now
                };

                // إضافة العنصر إلى القائمة
                contentItems.Add(newItem);

                // تحديث العرض
                LoadCurrentSectionContent();

                // تحديث شريط الحالة
                StatusText.Text = $"✅ تم إضافة محتوى جديد إلى قسم: {currentSection}";
            }
        }

        private void EditContent(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.Tag as ContentItem;
            EditContent(item);
        }

        private void EditContent(ContentItem item)
        {
            if (item != null)
            {
                // فتح نافذة تعديل المحتوى
                var inputDialog = new InputDialog(currentSection, item.Title, item.Content);
                inputDialog.Owner = this;

                if (inputDialog.ShowDialog() == true)
                {
                    // تحديث العنصر
                    item.Title = inputDialog.InputTitle;
                    item.Content = inputDialog.InputContent;

                    // حفظ البيانات فوراً
                    SaveData();

                    // تحديث العرض
                    LoadCurrentSectionContent();
                    ClearSelection();

                    // تحديث شريط الحالة
                    StatusText.Text = $"✅ تم تعديل المحتوى في قسم: {currentSection}";
                }
            }
        }

        private void CopyContent(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.Tag as ContentItem;
            CopyContent(item);
        }

        private async void CopyContent(ContentItem item)
        {
            if (item != null)
            {
                try
                {
                    // إذا كان هناك ملف مرفق ونص محتوى، نسخ النص أولاً ثم الملف
                    if (item.AttachedFiles.Count > 0 && !string.IsNullOrWhiteSpace(item.Content))
                    {
                        var filePath = item.AttachedFiles.First();
                        if (File.Exists(filePath))
                        {
                            // نسخ النص أولاً
                            Clipboard.SetText(item.Content);

                            // عرض رسالة Toast للنص
                            ToastWindow.Show("تم نسخ النص");

                            // تحديث شريط الحالة
                            StatusText.Text = $"📋 تم نسخ النص: {item.Title}";

                            // انتظار فترة 0.25 ثانية (250 ميلي ثانية)
                            await Task.Delay(250);

                            // ثم نسخ الملف
                            var fileList = new System.Collections.Specialized.StringCollection();
                            fileList.Add(filePath);
                            Clipboard.SetFileDropList(fileList);

                            // عرض رسالة Toast للملف
                            ToastWindow.Show($"تم نسخ الملف: {Path.GetFileName(filePath)}");

                            // تحديث شريط الحالة
                            StatusText.Text = $"📁 تم نسخ النص والملف معاً";
                        }
                        else
                        {
                            // الملف غير موجود، نسخ النص فقط
                            Clipboard.SetText(item.Content);
                            ToastWindow.Show("الملف المرفق غير موجود - تم نسخ النص");
                            StatusText.Text = $"📋 تم نسخ المحتوى: {item.Title}";
                        }
                    }
                    // إذا كان هناك ملف مرفق فقط (بدون نص)
                    else if (item.AttachedFiles.Count > 0)
                    {
                        var filePath = item.AttachedFiles.First();
                        if (File.Exists(filePath))
                        {
                            var fileList = new System.Collections.Specialized.StringCollection();
                            fileList.Add(filePath);
                            Clipboard.SetFileDropList(fileList);

                            // عرض رسالة Toast مؤقتة
                            ToastWindow.Show($"تم نسخ الملف: {Path.GetFileName(filePath)}");

                            // تحديث شريط الحالة
                            StatusText.Text = $"📁 تم نسخ الملف المرفق: {Path.GetFileName(filePath)}";
                        }
                        else
                        {
                            ToastWindow.Show("الملف المرفق غير موجود");
                            StatusText.Text = "❌ الملف المرفق غير موجود";
                        }
                    }
                    // إذا كان هناك نص فقط (بدون ملف)
                    else
                    {
                        // نسخ نص المحتوى فقط
                        Clipboard.SetText(item.Content);

                        // عرض رسالة Toast مؤقتة
                        ToastWindow.Show("تم نسخ النص");

                        // تحديث شريط الحالة
                        StatusText.Text = $"📋 تم نسخ المحتوى: {item.Title}";
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء النسخ:\n{ex.Message}",
                        "خطأ في النسخ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void DeleteContent(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.Tag as ContentItem;
            DeleteContent(item);
        }

        private void DeleteContent(ContentItem item)
        {
            if (item != null)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف '{item.Title}'؟",
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // حذف العنصر
                    contentItems.Remove(item);

                    // حفظ البيانات فوراً
                    SaveData();

                    // تحديث العرض
                    LoadCurrentSectionContent();
                    ClearSelection();

                    // تحديث شريط الحالة
                    StatusText.Text = $"✅ تم حذف المحتوى من قسم: {currentSection}";
                }
            }
        }

        private void ManageFiles(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.Tag as ContentItem;
            ManageFiles(item);
        }

        private void ManageFiles(ContentItem item)
        {
            if (item != null)
            {
                var fileWindow = new FileAttachmentWindow(item.Title, item.AttachedFiles);
                fileWindow.Owner = this;

                if (fileWindow.ShowDialog() == true)
                {
                    // نسخ الملفات إلى مجلد البرنامج وحفظ المسارات الجديدة
                    var newAttachedFiles = new List<string>();

                    foreach (var filePath in fileWindow.AttachedFiles)
                    {
                        var savedFilePath = SaveAttachedFile(filePath);
                        if (!string.IsNullOrEmpty(savedFilePath))
                        {
                            newAttachedFiles.Add(savedFilePath);
                        }
                    }

                    item.AttachedFiles = newAttachedFiles;
                    SaveData(); // حفظ البيانات فوراً
                    LoadCurrentSectionContent(); // تحديث العرض لإظهار عدد الملفات
                    ClearSelection();
                    StatusText.Text = $"📁 تم تحديث الملف المرفق: {item.Title} ({(item.AttachedFiles.Count > 0 ? "يوجد ملف" : "لا يوجد ملف")})";
                }
            }
        }

        private string SaveAttachedFile(string sourceFilePath)
        {
            try
            {
                if (!File.Exists(sourceFilePath))
                {
                    return sourceFilePath; // إذا كان الملف غير موجود، أعد المسار الأصلي
                }

                // إنشاء مجلد للملفات المرفقة
                var attachmentsFolder = Path.Combine(dataFolderPath, "Attachments");
                if (!Directory.Exists(attachmentsFolder))
                {
                    Directory.CreateDirectory(attachmentsFolder);
                }

                // الحصول على اسم الملف الأصلي
                var fileName = Path.GetFileName(sourceFilePath);

                // استخدام الاسم الأصلي للملف
                var destinationPath = Path.Combine(attachmentsFolder, fileName);

                // التحقق من وجود ملف بنفس الاسم وحذفه إذا وجد
                if (File.Exists(destinationPath))
                {
                    File.Delete(destinationPath);
                }

                // نسخ الملف
                File.Copy(sourceFilePath, destinationPath, true);

                System.Diagnostics.Debug.WriteLine($"تم نسخ الملف إلى: {destinationPath}");
                return destinationPath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الملف المرفق: {ex.Message}");
                return sourceFilePath; // في حالة الخطأ، أعد المسار الأصلي
            }
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void TaskMoveUpButton_Click(object sender, RoutedEventArgs e)
        {
            if (selectedItem != null)
            {
                MoveContentItem(selectedItem, -1);
            }
        }

        private void TaskMoveDownButton_Click(object sender, RoutedEventArgs e)
        {
            if (selectedItem != null)
            {
                MoveContentItem(selectedItem, 1);
            }
        }

        private void MoveContentItem(ContentItem item, int direction)
        {
            var sectionItems = contentItems.Where(x => x.Section == currentSection).ToList();
            var currentIndex = sectionItems.IndexOf(item);

            if (currentIndex == -1) return;

            var newIndex = currentIndex + direction;

            // التحقق من الحدود
            if (newIndex < 0 || newIndex >= sectionItems.Count) return;

            // العثور على المؤشرات في القائمة الأصلية
            var originalCurrentIndex = contentItems.IndexOf(item);
            var targetItem = sectionItems[newIndex];
            var originalTargetIndex = contentItems.IndexOf(targetItem);

            // تبديل العناصر
            contentItems[originalCurrentIndex] = targetItem;
            contentItems[originalTargetIndex] = item;

            // حفظ البيانات فوراً
            SaveData();

            // إعادة تحميل المحتوى
            LoadCurrentSectionContent();

            // إعادة تحديد العنصر
            selectedItem = item;

            StatusText.Text = $"✅ تم تحريك العنصر: {item.Title}";
        }
    }

    // فئة عنصر المحتوى
    public class ContentItem
    {
        public string Section { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public DateTime CreatedDate { get; set; }
        public List<string> AttachedFiles { get; set; } = new List<string>();
        public bool IsDivider { get; set; } = false;
        public string DividerText { get; set; } = "";
    }
}
