@echo off
chcp 65001 >nul 2>&1
title System Compatibility Check - Inzo IB v7.4

echo.
echo ========================================
echo   🔍 فحص توافق النظام - Inzo IB v7.4
echo ========================================
echo.

echo 📊 معلومات النظام:
echo ==================

:: نظام التشغيل
echo 🖥️  نظام التشغيل:
systeminfo | findstr /B /C:"OS Name" /C:"OS Version"
echo.

:: معمارية النظام
echo 🏗️  معمارية النظام:
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    echo ✅ x64 - متوافق
) else if "%PROCESSOR_ARCHITECTURE%"=="x86" (
    echo ❌ x86 - غير متوافق ^(يحتاج x64^)
) else (
    echo ⚠️  غير معروف: %PROCESSOR_ARCHITECTURE%
)
echo.

:: الذاكرة
echo 💾 الذاكرة المتاحة:
for /f "tokens=2 delims=:" %%a in ('systeminfo ^| findstr /C:"Total Physical Memory"') do echo    الذاكرة الكلية:%%a
for /f "tokens=2 delims=:" %%a in ('systeminfo ^| findstr /C:"Available Physical Memory"') do echo    الذاكرة المتاحة:%%a
echo.

:: مساحة القرص
echo 💿 مساحة القرص الحالي:
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do echo    المساحة المتاحة: %%a bytes
echo.

:: فحص الملفات المطلوبة
echo 📁 فحص الملفات المطلوبة:
echo ================================

if exist "InzoIB_v7.4_Simple.exe" (
    echo ✅ InzoIB_v7.4_Simple.exe - موجود
) else (
    echo ❌ InzoIB_v7.4_Simple.exe - مفقود
)

if exist "Data" (
    echo ✅ مجلد Data - موجود
) else (
    echo ⚠️  مجلد Data - غير موجود ^(سيتم إنشاؤه تلقائياً^)
)

if exist "coreclr.dll" (
    echo ✅ .NET Runtime - موجود
) else (
    echo ❌ .NET Runtime - مفقود
)

echo.

:: فحص الشبكة (اختياري)
echo 🌐 فحص الاتصال بالإنترنت:
ping -n 1 8.8.8.8 >nul 2>&1
if %errorlevel%==0 (
    echo ✅ الاتصال بالإنترنت متاح
) else (
    echo ⚠️  لا يوجد اتصال بالإنترنت ^(اختياري^)
)
echo.

:: التوصيات
echo 💡 التوصيات:
echo =============
echo • تأكد من أن نظام التشغيل Windows 10/11
echo • تأكد من وجود 4 GB ذاكرة على الأقل
echo • تأكد من وجود 500 MB مساحة فارغة
echo • أغلق برامج مكافحة الفيروسات مؤقتاً إذا واجهت مشاكل
echo.

echo 🎯 إذا كانت جميع الفحوصات ✅، يمكنك تشغيل البرنامج بأمان!
echo.

pause
