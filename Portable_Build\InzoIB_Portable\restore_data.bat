@echo off
chcp 65001 >nul 2>&1
title Restore Data - Inzo IB v7.4

echo.
echo ========================================
echo   🔄 استعادة النسخة الاحتياطية - Inzo IB v7.4
echo ========================================
echo.

:: التحقق من وجود مجلد النسخ الاحتياطية
if not exist "Backups" (
    echo ❌ خطأ: مجلد النسخ الاحتياطية غير موجود!
    echo تأكد من وجود نسخ احتياطية في مجلد "Backups".
    echo.
    pause
    exit /b 1
)

:: عرض النسخ الاحتياطية المتاحة
echo 📂 النسخ الاحتياطية المتاحة:
echo ==============================
set "count=0"
for /d %%i in (Backups\*) do (
    set /a count+=1
    echo !count!. %%~ni
)

if %count%==0 (
    echo ❌ لا توجد نسخ احتياطية متاحة!
    echo.
    pause
    exit /b 1
)

echo.
echo 0. إلغاء العملية
echo.

:: اختيار النسخة الاحتياطية
set /p "choice=اختر رقم النسخة الاحتياطية للاستعادة: "

if "%choice%"=="0" (
    echo تم إلغاء العملية.
    pause
    exit /b 0
)

:: التحقق من صحة الاختيار
if %choice% gtr %count% (
    echo ❌ اختيار غير صحيح!
    pause
    exit /b 1
)

:: الحصول على اسم النسخة الاحتياطية المختارة
set "selectedBackup="
set "currentCount=0"
for /d %%i in (Backups\*) do (
    set /a currentCount+=1
    if !currentCount!==%choice% (
        set "selectedBackup=%%~ni"
        goto :found
    )
)

:found
if "%selectedBackup%"=="" (
    echo ❌ خطأ في العثور على النسخة الاحتياطية!
    pause
    exit /b 1
)

set "backupPath=Backups\%selectedBackup%"
echo.
echo 📁 النسخة المختارة: %selectedBackup%
echo 📂 المسار: %backupPath%
echo.

:: عرض معلومات النسخة الاحتياطية إذا كانت متاحة
if exist "%backupPath%\📖 معلومات النسخة الاحتياطية.txt" (
    echo 📋 معلومات النسخة الاحتياطية:
    echo ===============================
    type "%backupPath%\📖 معلومات النسخة الاحتياطية.txt"
    echo.
)

:: تحذير قبل الاستعادة
echo ⚠️  تحذير مهم:
echo ===============
echo • ستتم استبدال جميع البيانات الحالية
echo • تأكد من عمل نسخة احتياطية من البيانات الحالية أولاً
echo • هذه العملية لا يمكن التراجع عنها
echo.

set /p "confirm=هل أنت متأكد من المتابعة؟ (y/n): "
if /i not "%confirm%"=="y" (
    echo تم إلغاء العملية.
    pause
    exit /b 0
)

echo.
echo 🔄 بدء عملية الاستعادة...
echo ========================

:: إنشاء مجلد البيانات إذا لم يكن موجوداً
if not exist "Data" (
    echo 📁 إنشاء مجلد البيانات...
    mkdir "Data"
)

if not exist "Data\Attachments" (
    echo 📁 إنشاء مجلد الملفات المرفقة...
    mkdir "Data\Attachments"
)

:: استعادة ملف البيانات الرئيسي
if exist "%backupPath%\content_data.json" (
    echo 📋 استعادة ملف البيانات الرئيسي...
    copy "%backupPath%\content_data.json" "Data\" >nul
    if %errorlevel%==0 (
        echo ✅ تم استعادة content_data.json
    ) else (
        echo ❌ فشل في استعادة content_data.json
    )
) else (
    echo ⚠️  ملف البيانات الرئيسي غير موجود في النسخة الاحتياطية
)

:: استعادة الملفات المرفقة
if exist "%backupPath%\Attachments" (
    echo 📎 استعادة الملفات المرفقة...
    
    :: حذف الملفات المرفقة الحالية
    if exist "Data\Attachments\*" (
        del "Data\Attachments\*" /Q >nul 2>&1
    )
    
    :: نسخ الملفات المرفقة من النسخة الاحتياطية
    xcopy "%backupPath%\Attachments\*" "Data\Attachments\" /E /I /Q >nul 2>&1
    if %errorlevel%==0 (
        echo ✅ تم استعادة الملفات المرفقة
    ) else (
        echo ⚠️  لا توجد ملفات مرفقة للاستعادة
    )
) else (
    echo ⚠️  مجلد الملفات المرفقة غير موجود في النسخة الاحتياطية
)

echo.
echo ========================================
echo           ✅ تمت الاستعادة بنجاح!
echo ========================================
echo.
echo 📁 تم استعادة البيانات من: %selectedBackup%
echo 📂 إلى مجلد: Data
echo.
echo 💡 نصائح:
echo • يمكنك الآن تشغيل البرنامج للتأكد من البيانات
echo • إذا واجهت مشاكل، يمكنك استعادة نسخة احتياطية أخرى
echo • اعمل نسخة احتياطية جديدة بعد التأكد من البيانات
echo.

set /p "runApp=هل تريد تشغيل البرنامج الآن؟ (y/n): "
if /i "%runApp%"=="y" (
    echo 🚀 تشغيل البرنامج...
    start "" "InzoIB_v7.4_Simple.exe"
)

echo.
pause
