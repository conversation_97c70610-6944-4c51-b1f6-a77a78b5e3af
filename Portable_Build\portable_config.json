{"buildConfiguration": {"name": "Inzo IB v7.4 - Portable Build", "version": "*******", "description": "Self-contained portable version of Inzo IB", "buildDate": "2025-01-07", "targetFramework": "net6.0-windows", "runtimeIdentifier": "win-x64", "selfContained": true, "singleFile": false, "trimmed": false, "compressionEnabled": true}, "portableFeatures": {"includeAllRuntimes": true, "includeNativeLibraries": true, "localDataStorage": true, "noRegistryDependencies": true, "noSystemPathDependencies": true, "portableSettings": true}, "dataManagement": {"dataFolder": "Data", "attachmentsFolder": "Data/Attachments", "configFile": "Data/content_data.json", "backupEnabled": true, "autoSave": true}, "systemRequirements": {"minimumOS": "Windows 10 version 1809", "recommendedOS": "Windows 11", "architecture": "x64", "minimumRAM": "4 GB", "recommendedRAM": "8 GB", "diskSpace": "200 MB"}, "buildInstructions": {"cleanBuild": "dotnet clean --configuration Release", "publishCommand": "dotnet publish -c Release -r win-x64 --self-contained true", "outputDirectory": "Portable_Build/InzoIB_Portable", "postBuildSteps": ["Create Data folder structure", "Copy existing data files", "Generate usage instructions", "Create launcher script"]}, "deploymentNotes": {"transferMethod": "Copy entire folder", "noInstallationRequired": true, "noAdminRightsRequired": true, "offlineCapable": true, "multipleInstancesSupported": true}}