# PowerShell Script لبناء النسخة المحمولة من Inzo IB
# تشغيل بصلاحيات عادية (لا يحتاج صلاحيات مدير)

param(
    [switch]$Clean = $false,
    [switch]$Verbose = $false
)

# إعداد الترميز
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   🚀 بناء النسخة المحمولة من Inzo IB" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# التحقق من وجود .NET 6.0
Write-Host "🔍 فحص .NET 6.0..." -ForegroundColor Blue
try {
    $dotnetVersion = dotnet --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "dotnet command not found"
    }
    Write-Host "✅ تم العثور على .NET: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ خطأ: .NET 6.0 غير مثبت على النظام" -ForegroundColor Red
    Write-Host "يرجى تثبيت .NET 6.0 SDK من: https://dotnet.microsoft.com/download" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# الانتقال إلى مجلد المشروع
$projectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $projectRoot

# تنظيف البناء السابق إذا طُلب ذلك
if ($Clean) {
    Write-Host "🧹 تنظيف البناء السابق..." -ForegroundColor Blue
    dotnet clean --configuration Release --verbosity quiet
    if (Test-Path "Portable_Build\InzoIB_Portable") {
        Remove-Item "Portable_Build\InzoIB_Portable" -Recurse -Force
    }
}

# إنشاء مجلد البناء
$buildPath = "Portable_Build\InzoIB_Portable"
if (!(Test-Path "Portable_Build")) {
    New-Item -ItemType Directory -Path "Portable_Build" | Out-Null
}

# بناء النسخة المحمولة
Write-Host "🔨 بناء النسخة المحمولة..." -ForegroundColor Blue
$verbosityLevel = if ($Verbose) { "normal" } else { "quiet" }

$publishArgs = @(
    "publish"
    "-c", "Release"
    "-r", "win-x64"
    "--self-contained", "true"
    "-o", $buildPath
    "--verbosity", $verbosityLevel
)

& dotnet @publishArgs

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في بناء النسخة المحمولة" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# إنشاء مجلد البيانات
Write-Host "📁 إنشاء مجلد البيانات..." -ForegroundColor Blue
$dataPath = Join-Path $buildPath "Data"
$attachmentsPath = Join-Path $dataPath "Attachments"

if (!(Test-Path $dataPath)) {
    New-Item -ItemType Directory -Path $dataPath | Out-Null
}
if (!(Test-Path $attachmentsPath)) {
    New-Item -ItemType Directory -Path $attachmentsPath | Out-Null
}

# نسخ ملف البيانات إذا كان موجوداً
$contentDataFile = "content_data.json"
if (Test-Path $contentDataFile) {
    Write-Host "📋 نسخ ملف البيانات..." -ForegroundColor Blue
    Copy-Item $contentDataFile -Destination $dataPath
}

# نسخ الملفات المرفقة إذا كانت موجودة
$sourceAttachments = "bin\Release\net6.0-windows\Data\Attachments"
if (Test-Path $sourceAttachments) {
    Write-Host "📎 نسخ الملفات المرفقة..." -ForegroundColor Blue
    Copy-Item "$sourceAttachments\*" -Destination $attachmentsPath -Recurse -Force -ErrorAction SilentlyContinue
}

# إنشاء ملف التعليمات
Write-Host "📝 إنشاء ملف التعليمات..." -ForegroundColor Blue
$instructionsContent = @"
🎯 Inzo IB v7.4 - النسخة المحمولة
=====================================

📦 هذه النسخة المحمولة تحتوي على:
• جميع ملفات .NET Runtime المطلوبة
• جميع المكتبات والتبعيات
• مجلد البيانات والملفات المرفقة
• لا تحتاج لتثبيت أي برامج إضافية

🚀 كيفية الاستخدام:
1. انقر مرتين على "تشغيل البرنامج.bat"
   أو انقر مرتين على "InzoIB_v7.4_Simple.exe"

2. للنقل إلى جهاز آخر:
   • انسخ مجلد "InzoIB_Portable" كاملاً
   • الصقه في أي مكان في الجهاز الجديد
   • شغل البرنامج مباشرة

📁 هيكل الملفات:
├── InzoIB_v7.4_Simple.exe     # البرنامج الرئيسي
├── تشغيل البرنامج.bat          # ملف التشغيل السريع
├── Data/                      # مجلد البيانات
│   ├── content_data.json      # ملف البيانات
│   └── Attachments/           # الملفات المرفقة
└── ملفات .NET Runtime...      # ملفات النظام المطلوبة

✅ مميزات النسخة المحمولة:
• تعمل على أي جهاز Windows 10/11
• لا تحتاج تثبيت .NET Framework
• لا تحتاج صلاحيات مدير النظام
• جميع البيانات محفوظة محلياً
• سرعة في التشغيل

📞 للدعم الفني: تواصل مع فريق التطوير
📅 تاريخ البناء: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
🏷️ إصدار البرنامج: Inzo IB v7.4 - Portable
"@

$instructionsPath = Join-Path $buildPath "📖 تعليمات الاستخدام.txt"
$instructionsContent | Out-File -FilePath $instructionsPath -Encoding UTF8

# إنشاء ملف تشغيل سريع
Write-Host "🚀 إنشاء ملف التشغيل السريع..." -ForegroundColor Blue
$launcherContent = @'
@echo off
chcp 65001 >nul
title Inzo IB v7.4 - Simple Version

echo 🚀 تشغيل Inzo IB v7.4...
echo.
start "" "InzoIB_v7.4_Simple.exe"
exit
'@

$launcherPath = Join-Path $buildPath "تشغيل البرنامج.bat"
$launcherContent | Out-File -FilePath $launcherPath -Encoding UTF8

# حساب حجم المجلد
$folderSize = (Get-ChildItem $buildPath -Recurse | Measure-Object -Property Length -Sum).Sum
$folderSizeMB = [math]::Round($folderSize / 1MB, 2)

Write-Host ""
Write-Host "✅ تم بناء النسخة المحمولة بنجاح!" -ForegroundColor Green
Write-Host "📁 المجلد: $buildPath" -ForegroundColor Cyan
Write-Host "📊 حجم المجلد: $folderSizeMB MB" -ForegroundColor Cyan
Write-Host ""
Write-Host "🎯 يمكنك الآن نسخ مجلد 'InzoIB_Portable' إلى أي جهاز آخر" -ForegroundColor Yellow
Write-Host "   والبرنامج سيعمل بدون الحاجة لتثبيت أي شيء إضافي!" -ForegroundColor Yellow
Write-Host ""

# فتح مجلد البناء
$openFolder = Read-Host "هل تريد فتح مجلد البناء؟ (y/n)"
if ($openFolder -eq 'y' -or $openFolder -eq 'Y') {
    Start-Process explorer.exe -ArgumentList $buildPath
}

Write-Host "اضغط Enter للخروج..." -ForegroundColor Gray
Read-Host
