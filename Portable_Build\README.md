# 🚀 Inzo IB v7.4 - النسخة المحمولة

## 📋 نظرة عامة

هذا المجلد يحتوي على أدوات بناء النسخة المحمولة من برنامج Inzo IB v7.4. النسخة المحمولة تتضمن جميع المتطلبات والتبعيات اللازمة لتشغيل البرنامج على أي جهاز Windows بدون الحاجة لتثبيت برامج إضافية.

## 🎯 المميزات

- ✅ **محمول بالكامل**: لا يحتاج تثبيت .NET Framework أو أي برامج إضافية
- ✅ **سهل النقل**: انسخ والصق المجلد إلى أي جهاز آخر
- ✅ **بيانات محلية**: جميع البيانات والملفات محفوظة في مجلد البرنامج
- ✅ **أمان عالي**: لا يحتاج صلاحيات مدير النظام
- ✅ **سرعة في التشغيل**: تحسينات في الأداء

## 🛠️ كيفية البناء

### الطريقة الأولى: استخدام Batch Script
```cmd
# تشغيل من Command Prompt
cd Portable_Build
build_portable.bat
```

### الطريقة الثانية: استخدام PowerShell (مُوصى به)
```powershell
# تشغيل من PowerShell
cd Portable_Build
.\build_portable.ps1

# أو مع خيارات إضافية
.\build_portable.ps1 -Clean -Verbose
```

### خيارات PowerShell Script:
- `-Clean`: تنظيف البناء السابق قبل البناء الجديد
- `-Verbose`: عرض تفاصيل أكثر أثناء عملية البناء

## 📁 هيكل الملفات بعد البناء

```
Portable_Build/
├── InzoIB_Portable/                    # المجلد المحمول النهائي
│   ├── InzoIB_v7.4_Simple.exe         # البرنامج الرئيسي
│   ├── تشغيل البرنامج.bat              # ملف التشغيل السريع
│   ├── 📖 تعليمات الاستخدام.txt        # دليل الاستخدام
│   ├── Data/                           # مجلد البيانات
│   │   ├── content_data.json           # ملف البيانات الرئيسي
│   │   └── Attachments/                # الملفات المرفقة
│   └── [ملفات .NET Runtime]            # ملفات النظام المطلوبة
├── build_portable.bat                  # أداة البناء (Batch)
├── build_portable.ps1                  # أداة البناء (PowerShell)
└── README.md                           # هذا الملف
```

## 🔧 المتطلبات للبناء

- Windows 10/11
- .NET 6.0 SDK أو أحدث
- PowerShell 5.1 أو أحدث (للـ PowerShell script)

## 📦 كيفية النقل إلى جهاز آخر

1. **بعد البناء الناجح**:
   - ستجد مجلد `InzoIB_Portable` في مجلد `Portable_Build`

2. **للنقل**:
   - انسخ مجلد `InzoIB_Portable` كاملاً
   - الصقه في أي مكان في الجهاز الجديد
   - لا تحتاج لأي إعدادات إضافية

3. **للتشغيل**:
   - انقر مرتين على `تشغيل البرنامج.bat`
   - أو انقر مرتين على `InzoIB_v7.4_Simple.exe`

## 🔍 استكشاف الأخطاء

### مشكلة: "dotnet command not found"
**الحل**: تثبيت .NET 6.0 SDK من [الموقع الرسمي](https://dotnet.microsoft.com/download)

### مشكلة: فشل في البناء
**الحل**: 
1. تشغيل `build_portable.ps1 -Clean` لتنظيف البناء السابق
2. التأكد من عدم تشغيل البرنامج أثناء البناء
3. التأكد من وجود صلاحيات الكتابة في المجلد

### مشكلة: حجم المجلد كبير
**الحل**: هذا طبيعي للنسخة المحمولة (حوالي 150-200 MB) لأنها تتضمن جميع ملفات .NET Runtime

## 📞 الدعم الفني

للمساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## 📅 سجل التحديثات

- **v7.4**: إضافة النسخة المحمولة مع دعم كامل لـ Self-Contained Deployment
- تحسينات في الأداء والاستقرار
- دعم أفضل لنقل البيانات بين الأجهزة

---

**ملاحظة**: هذه النسخة المحمولة مُحسَّنة للاستخدام على أجهزة Windows 10/11 مع معمارية x64.
