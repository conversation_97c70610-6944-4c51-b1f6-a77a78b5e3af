@echo off
echo Testing Portable Version...
echo.

cd /d "%~dp0InzoIB_Portable"

echo Checking files...
if exist "InzoIB_v7.4_Simple.exe" (
    echo [OK] Main executable found
) else (
    echo [ERROR] Main executable missing
    pause
    exit /b 1
)

if exist "Data" (
    echo [OK] Data folder found
) else (
    echo [ERROR] Data folder missing
    pause
    exit /b 1
)

if exist "coreclr.dll" (
    echo [OK] .NET Runtime found
) else (
    echo [ERROR] .NET Runtime missing
    pause
    exit /b 1
)

echo.
echo All checks passed!
echo You can now run the portable version.
echo.
pause
